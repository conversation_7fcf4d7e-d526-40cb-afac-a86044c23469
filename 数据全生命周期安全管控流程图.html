<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据全生命周期安全管控流程图 (Optimized)</title>
    <style>
        body {
            font-family: '<PERSON>l', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .flowchart-title-main {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 25px;
        }

        .lifecycle-flowchart-container {
            width: 1200px; 
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            position: relative; 
            padding: 25px; /* Container padding */
            display: flex; 
            justify-content: space-between; 
            align-items: stretch; /* Makes all stage boxes equal height */
        }

        .lifecycle-stage {
            background-color: #fdfdfd; 
            border: 1px solid #ced4da;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.04);
            width: 206px; /* Adjusted width: (1150 content_width - 4*30 gap_width) / 5 stages */
            padding: 15px;
            display: flex; /* For internal vertical alignment */
            flex-direction: column; /* Stack title and controls vertically */
            position: relative; 
        }

        .lifecycle-stage .stage-title {
            font-size: 16px;
            font-weight: bold;
            color: #0056b3; 
            text-align: center;
            padding-bottom: 10px;
            margin-bottom: 10px;
            border-bottom: 2px solid #0056b3; 
            flex-shrink: 0; /* Prevent title from shrinking */
        }

        .lifecycle-stage .stage-controls {
            list-style-type: none; 
            padding-left: 0;
            margin: 0;
            font-size: 13px;
            line-height: 1.6;
            color: #495057; 
            flex-grow: 1; /* Allow this list to take available vertical space */
            padding-bottom: 5px; /* Ensure some space at the bottom */
        }

        .lifecycle-stage .stage-controls li {
            padding-left: 18px; 
            position: relative;
            margin-bottom: 8px;
        }
        .lifecycle-stage .stage-controls li::before {
            content: "▪"; 
            position: absolute;
            left: 0;
            top: 1px;
            color: #007bff; 
            font-size: 14px;
        }

        .connector-svg-layer {
            position: absolute;
            top: 0; /* Aligns with flowchart-container padding box */
            left: 0; /* Aligns with flowchart-container padding box */
            width: 100%; /* Spans flowchart-container's width (including padding) */
            height: 100%;/* Spans flowchart-container's height (including padding) */
            pointer-events: none; 
        }
    </style>
</head>
<body>
    <div class="flowchart-title-main">数据全生命周期安全管控流程图</div>
    <div class="lifecycle-flowchart-container" id="lifecycleContainer">
        
        <div class="lifecycle-stage" id="stageCollect">
            <div class="stage-title">1. 数据采集</div>
            <ul class="stage-controls">
                <li>安全终端部署</li>
                <li>合法/真实/完整性保障</li>
                <li>敏感数据实时加密/脱敏</li>
            </ul>
        </div>

        <div class="lifecycle-stage" id="stageStore">
            <div class="stage-title">2. 数据存储</div>
            <ul class="stage-controls">
                <li>国密算法加密存储</li>
                <li>密钥芯片级安全存储</li>
                <li>数据备份与灾难恢复</li>
            </ul>
        </div>

        <div class="lifecycle-stage" id="stageProcess">
            <div class="stage-title">3. 数据处理</div>
            <ul class="stage-controls">
                <li>可信执行环境(TEE)处理</li>
                <li>操作行为全记录与审计</li>
            </ul>
        </div>

        <div class="lifecycle-stage" id="stageShare">
            <div class="stage-title">4. 数据共享</div>
            <ul class="stage-controls">
                <li>规范化审批流程</li>
                <li>API网关统一管理</li>
                <li>隐私计算 (可用不可见)</li>
            </ul>
        </div>

        <div class="lifecycle-stage" id="stageDestroy">
            <div class="stage-title">5. 数据销毁</div>
            <ul class="stage-controls">
                <li>数据销毁策略制定</li>
                <li>安全销毁与操作记录</li>
            </ul>
        </div>

        <svg class="connector-svg-layer" id="lifecycleConnectorCanvas">
            <defs>
                <marker id="lifecycleArrowhead"
                    viewBox="0 0 10 10" refX="8" refY="5" 
                    markerUnits="strokeWidth" markerWidth="7" markerHeight="5" 
                    orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#0056b3" />
                </marker>
            </defs>
            <!-- Lines will be drawn by JavaScript -->
        </svg>
    </div>

<script>
    window.addEventListener('load', () => {
        const container = document.getElementById('lifecycleContainer');
        const svgCanvas = document.getElementById('lifecycleConnectorCanvas');
        const svgNS = "http://www.w3.org/2000/svg";

        const stageIds = ['stageCollect', 'stageStore', 'stageProcess', 'stageShare', 'stageDestroy'];
        const stages = stageIds.map(id => document.getElementById(id));

        // Wait for images and other resources if any, or a slight delay for flexbox to fully settle
        // For simple layouts like this, 'load' is often enough. For complex ones, a small setTimeout might be needed.
        // requestAnimationFrame can also be used for smoother rendering if positions are animated or change frequently.
        
        // Ensure container has a determined size before calculating relative positions
        if (container.offsetWidth === 0) {
            console.warn("Container not yet rendered with size. Arrows might be misplaced.");
            // You might want to use a MutationObserver or ResizeObserver if the container size changes later
        }

        for (let i = 0; i < stages.length - 1; i++) {
            if (!stages[i] || !stages[i+1]) {
                console.error("Stage element not found for arrow drawing:", stageIds[i], "or", stageIds[i+1]);
                continue;
            }
            
            const stage1Rect = stages[i].getBoundingClientRect();
            const stage2Rect = stages[i+1].getBoundingClientRect();
            const containerRect = container.getBoundingClientRect(); // SVG is relative to this container

            // Calculate Y position for the arrow (vertical center of the stage box)
            // Since align-items: stretch makes all boxes same height, we can use any box's height for mid-Y calculation.
            // Or, more robustly, use the specific stage's mid-Y if heights could vary slightly due to borders/paddings.
            const yPos = (stage1Rect.top + stage1Rect.height / 2) - containerRect.top;

            // X position for start of arrow (right edge of current stage box)
            const x1 = (stage1Rect.right - containerRect.left) ; 
            // X position for end of arrow (left edge of next stage box)
            const x2 = (stage2Rect.left - containerRect.left) ;

            const line = document.createElementNS(svgNS, "line");
            line.setAttribute('x1', String(x1));
            line.setAttribute('y1', String(yPos));
            line.setAttribute('x2', String(x2));
            line.setAttribute('y2', String(yPos));
            line.setAttribute('stroke', '#0056b3');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('marker-end', 'url(#lifecycleArrowhead)');
            
            svgCanvas.appendChild(line);
        }
    });
</script>

</body>
</html>