<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberSec核心技术赋能数据要素化与价值创造流程图 (Optimized Layout & Arrows)</title>
    <style>
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .flowchart-title-main {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
        }

        .empowerment-flowchart-container {
            width: 1300px; 
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            position: relative;
            padding: 25px;
            display: flex;
            justify-content: space-between;
            align-items: stretch; 
        }

        .flow-stage {
            background-color: #fdfdfd;
            border: 1px solid #ced4da;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.04);
            padding: 15px;
            display: flex;
            flex-direction: column;
            text-align: center; 
            position: relative; 
        }

        .flow-stage.input-stage { width: 220px; border-left: 4px solid #6c757d; }
        .flow-stage.empowerment-stage { width: 400px; border-left: 4px solid #007bff; }
        .flow-stage.factor-stage { width: 260px; border-left: 4px solid #28a745; }
        .flow-stage.value-stage { width: 260px; border-left: 4px solid #ffc107; }


        .flow-stage .stage-main-title {
            font-size: 17px;
            font-weight: bold;
            color: #333;
            padding-bottom: 10px;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            flex-shrink: 0; 
        }

        /* CSS Change for vertical centering of content */
        .flow-stage .stage-content {
            font-size: 13px;
            line-height: 1.6;
            color: #495057;
            text-align: left; 
            flex-grow: 1; /* Allows this div to take up available vertical space */
            display: flex; /* Use flex to center its child (the ul) */
            flex-direction: column;
            justify-content: center; /* Vertically centers the ul within this content div */
        }
        .flow-stage .stage-content ul,
        .tech-block .tech-details ul { /* tech-details ul might also benefit though less critical */
            list-style-type: none;
            padding-left: 0;
            margin: 0; /* Remove default ul margins */
        }
        .flow-stage .stage-content li,
        .tech-block .tech-details li {
            padding-left: 18px;
            position: relative;
            margin-bottom: 6px;
        }
        .flow-stage .stage-content li::before,
        .tech-block .tech-details li::before {
            content: "▪";
            position: absolute;
            left: 0;
            top: 1px;
            color: #007bff; 
            font-size: 14px;
        }
        
        .empowerment-stage .stage-main-title { color: #0056b3; }
        .tech-block-container {
            display: flex;
            flex-direction: column;
            gap: 10px; 
            flex-grow: 1;
            justify-content: center; 
        }
        .tech-block {
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 10px;
            background-color: #f9f9f9;
            min-height: 70px; /* Ensure tech blocks have some min height */
            display: flex; /* For centering content if it's short */
            flex-direction: column;
            justify-content: center;
        }
        .tech-block .tech-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
        }
        .tech-block.trust-root { border-left: 3px solid #FFA07A; } 
        .tech-block.trust-root .tech-title { color: #D2691E; }
        .tech-block.trust-root .tech-details li::before {color: #FFA07A;}

        .tech-block.guomi-stack { border-left: 3px solid #90EE90; } 
        .tech-block.guomi-stack .tech-title { color: #2E8B57; }
        .tech-block.guomi-stack .tech-details li::before {color: #90EE90;}
        
        .tech-block.audit-ai { border-left: 3px solid #87CEEB; } 
        .tech-block.audit-ai .tech-title { color: #4682B4; }
        .tech-block.audit-ai .tech-details li::before {color: #87CEEB;}

        .flow-stage.factor-stage .stage-content li::before {color: #28a745;}
        .flow-stage.value-stage .stage-content li::before {color: #ffc107;}


        .connector-svg-layer {
            position: absolute;
            top: 0; left: 0;
            width: 100%; height: 100%;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="flowchart-title-main">CyberSec核心技术赋能数据要素化与价值创造流程图</div>
    <div class="empowerment-flowchart-container" id="empowermentContainer">
        
        <div class="flow-stage input-stage" id="rawDataStage">
            <div class="stage-main-title">多元原始数据</div>
            <div class="stage-content">
                <ul>
                    <li>公共数据、企业数据</li>
                    <li>物联网感知数据等</li>
                    <li>(经初步标准化)</li>
                </ul>
            </div>
        </div>

        <div class="flow-stage empowerment-stage" id="empowermentCoreStage">
            <div class="stage-main-title">CyberSec核心技术赋能</div>
            <div class="tech-block-container">
                <div class="tech-block trust-root" id="techTrustRoot">
                    <div class="tech-title">硬件信任根</div>
                    <div class="tech-details">
                        <ul>
                            <li>赋能: 物理级安全基座</li>
                            <li>特性: 不可更改、不可绕过</li>
                        </ul>
                    </div>
                </div>
                <div class="tech-block guomi-stack" id="techGuomiStack">
                    <div class="tech-title">国密全栈</div>
                    <div class="tech-details">
                        <ul>
                            <li>赋能: 自主可控高效处理</li>
                            <li>特性: 数据加密、身份认证</li>
                        </ul>
                    </div>
                </div>
                <div class="tech-block audit-ai" id="techIntelligentAudit">
                    <div class="tech-title">智能审计</div>
                    <div class="tech-details">
                        <ul>
                            <li>赋能: 全链路可信追溯</li>
                            <li>特性: 透明监管、风险预警</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="flow-stage factor-stage" id="dataFactorStage">
            <div class="stage-main-title">安全可信数据要素</div>
            <div class="stage-content">
                <ul>
                    <li>特性: 可用不可见</li>
                    <li>特性: 可控可计量</li>
                    <li>成果: 流通率大幅提升</li>
                </ul>
            </div>
        </div>

        <div class="flow-stage value-stage" id="valueCreationStage">
            <div class="stage-main-title">核心价值创造</div>
            <div class="stage-content">
                <ul>
                    <li>决策智能化</li>
                    <li>运营效率提升</li>
                    <li>要素安全可控</li>
                    <li>合规成本降低</li>
                </ul>
            </div>
        </div>

        <svg class="connector-svg-layer" id="empowermentConnectorCanvas">
            <defs>
                <marker id="empowermentArrowhead"
                    viewBox="0 0 10 10" refX="8" refY="5" 
                    markerUnits="strokeWidth" markerWidth="7" markerHeight="5" 
                    orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#0056b3" />
                </marker>
                 <marker id="empowermentTechArrowhead"
                    viewBox="0 0 10 10" refX="8" refY="5" 
                    markerUnits="strokeWidth" markerWidth="6" markerHeight="4" 
                    orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#555" />
                </marker>
            </defs>
            <!-- Lines drawn by JavaScript -->
        </svg>
    </div>

<script>
    window.addEventListener('load', () => {
        const container = document.getElementById('empowermentContainer');
        const svgCanvas = document.getElementById('empowermentConnectorCanvas');
        const svgNS = "http://www.w3.org/2000/svg";

        function getPortPosition(elementId, portSide, 
                                 relativeToContainer, 
                                 offsetPercentage = 0.5, 
                                 edgeOverlap = 0) { // Positive overlap means line starts/ends INSIDE the element
            const el = document.getElementById(elementId);
            if (!el) { console.error("Element not found:", elementId); return null; }
            const elRect = el.getBoundingClientRect();
            const containerRect = relativeToContainer.getBoundingClientRect();

            let x, y;
            switch (portSide) {
                case 'left':
                    x = elRect.left - containerRect.left + edgeOverlap; // Add overlap to move right (into element)
                    y = elRect.top - containerRect.top + elRect.height * offsetPercentage;
                    break;
                case 'right':
                    x = elRect.right - containerRect.left - edgeOverlap; // Subtract overlap to move left (into element)
                    y = elRect.top - containerRect.top + elRect.height * offsetPercentage;
                    break;
                case 'top':
                    x = elRect.left - containerRect.left + elRect.width * offsetPercentage;
                    y = elRect.top - containerRect.top + edgeOverlap; // Add overlap to move down
                    break;
                case 'bottom':
                    x = elRect.left - containerRect.left + elRect.width * offsetPercentage;
                    y = elRect.bottom - containerRect.top - edgeOverlap; // Subtract overlap to move up
                    break;
                default: 
                    x = elRect.left - containerRect.left + elRect.width / 2;
                    y = elRect.top - containerRect.top + elRect.height / 2;
            }
            return { x, y };
        }

        function drawLine(p1, p2, options = {}) {
            if (!p1 || !p2) return;
            const line = document.createElementNS(svgNS, "line");
            line.setAttribute('x1', String(p1.x));
            line.setAttribute('y1', String(p1.y));
            line.setAttribute('x2', String(p2.x));
            line.setAttribute('y2', String(p2.y));
            line.setAttribute('stroke', options.stroke || '#0056b3');
            line.setAttribute('stroke-width', options.strokeWidth || '2.5'); // Increased stroke-width for main arrows
            if (options.markerEndId) line.setAttribute('marker-end', `url(#${options.markerEndId})`);
            if (options.dashArray) line.setAttribute('stroke-dasharray', options.dashArray);
            svgCanvas.appendChild(line);
        }

        const ARROW_OVERLAP = 10; // How many pixels the arrow should overlap into the stage box

        // Main flow arrows - with overlap
        const rawDataPosR = getPortPosition('rawDataStage', 'right', container, 0.5, ARROW_OVERLAP);
        const empowerPosL = getPortPosition('empowermentCoreStage', 'left', container, 0.5, ARROW_OVERLAP);
        drawLine(rawDataPosR, empowerPosL, { markerEndId: 'empowermentArrowhead' });

        const empowerPosR = getPortPosition('empowermentCoreStage', 'right', container, 0.5, ARROW_OVERLAP);
        const factorPosL = getPortPosition('dataFactorStage', 'left', container, 0.5, ARROW_OVERLAP);
        drawLine(empowerPosR, factorPosL, { markerEndId: 'empowermentArrowhead' });

        const factorPosR = getPortPosition('dataFactorStage', 'right', container, 0.5, ARROW_OVERLAP);
        const valuePosL = getPortPosition('valueCreationStage', 'left', container, 0.5, ARROW_OVERLAP);
        drawLine(factorPosR, valuePosL, { markerEndId: 'empowermentArrowhead' });

        // Arrows within Empowerment Stage (connecting tech blocks vertically)
        const TECH_ARROW_OVERLAP = 5; // Smaller overlap for internal arrows
        const trustRootPosB = getPortPosition('techTrustRoot', 'bottom', container, 0.5, TECH_ARROW_OVERLAP); 
        const guomiStackPosT = getPortPosition('techGuomiStack', 'top', container, 0.5, TECH_ARROW_OVERLAP);
        drawLine(trustRootPosB, guomiStackPosT, { stroke: '#777', strokeWidth: '1.5', markerEndId: 'empowermentTechArrowhead' });
        
        const guomiStackPosB = getPortPosition('techGuomiStack', 'bottom', container, 0.5, TECH_ARROW_OVERLAP);
        const auditAiPosT = getPortPosition('techIntelligentAudit', 'top', container, 0.5, TECH_ARROW_OVERLAP);
        drawLine(guomiStackPosB, auditAiPosT, { stroke: '#777', strokeWidth: '1.5', markerEndId: 'empowermentTechArrowhead' });
    });
</script>

</body>
</html>