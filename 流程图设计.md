### 一、核心必选流程图 (强烈建议设计)

* **CyberSec系统整体技术架构图 (最重要)**

  * **对应章节：** **「第二部分: 参赛项目介绍 - 二、解决方案 - (二) 技术路线 - 1. 技术架构」**
  * **图表内容：**

    * **清晰展示您在申报书中描述的**"端-管-云"三层一体化可信数据流转安全运行体系**。**
    * **端侧（硬件信任与执行层）**：突出自主可控安全计算终端（搭载"赛安"系列芯片）、硬件级可信执行环境（TEE）、轻量化安全操作系统与国密密码模块。标注“物理不可破”。
    * **管侧（安全传输与防护层）**：展示国密安全通信协议栈、智能安全管控网关、可信VPN/专网通道。标注“传输不可窃”。
    * **云端/中心（智能治理与服务层）**：体现区块链智能审计中台、数据安全治理中心、隐私计算与数据服务引擎。标注“行为可追溯、权责可界定、数据可用不可见”。
    * **用箭头清晰表示各层之间、各核心组件之间的主要数据流、控制流和信任传递关系。**
  * **模板建议：**

    * **采用**专业的、信息量适中的分层架构图**（类似您提供的图2风格，但根据您的三层架构进行定制）。**
    * **使用不同颜色背景或区域明确划分三层。**
    * **组件用圆角矩形表示，内部文字简明扼要。**
    * **可以在「项目简述」中放置一个此架构图的**高度简化版本**，仅突出“硬件信任根+国密全栈+智能审计”三大核心支柱及其关系。**
  * **信心评估：** **对此图的必要性和内容建议为** **高度自信 (>90%)**。
* **数据全生命周期安全管控流程图**

  * **对应章节：** **「第二部分: 参赛项目介绍 - 二、解决方案 - (三) 数据治理 - 3. 全生命周期安全管控」**
  * **图表内容：**

    * **以时间或逻辑顺序，展示数据从**采集 -> 存储 -> 处理 -> 共享 -> 销毁**的整个生命周期。**
    * **在每个阶段清晰标注CyberSec系统提供的关键安全措施和技术保障（例如：采集阶段的合法性校验与加密；存储阶段的国密加密与安全存储；处理阶段的TEE与审计；共享阶段的权限控制与隐私计算；销毁阶段的安全擦除与记录）。**
  * **模板建议：**

    * **水平或垂直的阶段性流程图**（类似您提供的图3，用不同色块区分阶段）。
    * **每个阶段下方或旁边列出关键安全活动/技术。**
    * **简洁的图标可以辅助说明。**
  * **信心评估：** **对此图的必要性和内容建议为** **高度自信 (>90%)**。
* **CyberSec核心技术赋能数据要素化与价值创造流程图**

  * **对应章节：** **「第二部分: 参赛项目介绍 - 二、解决方案 - (一) 数据要素基础 - 数据技术驱动与价值赋能」**
  * **图表内容：**

    * **左侧输入：多元化的原始数据（公共数据、企业数据等）。**
    * **中间核心处理：展示**"硬件信任根"（保障安全基础）、"国密全栈"（实现自主可控处理）、"智能审计"（确保可信透明）**三大核心技术如何协同作用于数据。**
    * **右侧输出：经过赋能的高价值、安全可信的数据要素。**
    * **下方或延伸：这些数据要素如何驱动多维度成效（赋能决策智能化、驱动运营效率倍增、保障要素安全可控、促进合规成本降低等）。**
  * **模板建议：**

    * **输入-处理-输出（IPO）型流程图**，中间处理部分可以突出三大核心技术模块。
    * **风格可以参考图2，体现专业性和技术深度。**
    * **可适当使用图标代表数据、技术模块和价值成效。**
  * **信心评估：** **对此图的必要性和内容建议为** **高度自信 (>90%)**。

### 二、推荐设计流程图 (显著提升表达效果)

* **某项关键机制/模式创新示意图 (例如：“算法即服务”与“数据要素信托”模式)**

  * **对应章节：** **「第二部分: 参赛项目介绍 - 二、解决方案 - (四) 机制创新与模式创新 - 2. 模式创新」**
  * **图表内容：**

    * **清晰展示该创新模式下的**主要参与方**（如数据所有者、CyberSec平台、算法服务使用者、监管方等）。**
    * **描述**核心业务流程**（如数据托管、算法封装、服务调用、安全计算、结果反馈、收益分配、合规审计等）。**
    * **体现关键的**信息流、价值流和信任流**。**
  * **模板建议：**

    * **如果角色和其独立流程突出，可采用**泳道图**（类似您提供的图4）。**
    * **如果更侧重交互关系，可采用**自由连接的业务生态图或交互图**。**
    * **颜色和线条应清晰区分不同流向。**
  * **信心评估：** **对此图的价值和内容建议为** **相对确信 (60-90%)**，选择最能体现项目创新性的1-2个模式进行图示。
* **数据采集与高质量汇聚流程图**

  * **对应章节：** **「第二部分: 参赛项目介绍 - 二、解决方案 - (一) 数据要素基础 - 数据多元化数据采集与高质量汇聚」**
  * **图表内容：**

    * **展示数据从**多个异构源头**（如公共数据、企业自有数据、物联网感知数据等，可配小图标）开始。**
    * **经过CyberSec平台的**标准化安全接入**（API接口、安全组件）。**
    * **进行**初步的标准化处理**（数据清洗、格式统一、元数据提取、质量控制）。**
    * **最终形成**高质量的数据基底**进入后续处理。**
  * **模板建议：**

    * **线性流程为主，可在数据源处有分支汇入**。节点清晰，连接明确。
    * **风格可以简洁明了，参考图1的节点和连接方式，但颜色和细节上更专业些。**
  * **信心评估：** **对此图的价值和内容建议为** **相对确信 (60-90%)**。

### 三、可选补充图表 (根据篇幅和侧重点决定)

* **“制度+技术+组织”三位一体数据治理框架图**

  * **对应章节：** **「第二部分: 参赛项目介绍 - 二、解决方案 - (三) 数据治理 - 1. 数据治理框架」**
  * **图表内容：** **以图形化方式（非传统流程图）展示制度、技术、组织三个维度如何相互支撑，共同构成本项目的数据治理体系，并指向核心治理目标（如安全、合规、价值释放）。**
  * **模板建议：** **三角形结构图、金字塔图、或中心辐射型的概念关系图**。
  * **信心评估：** **对此图的价值和内容建议为** **中度推测 (30-60%)**，文本描述也比较清晰，图能锦上添花。
* **CyberSec系统在典型应用场景中的部署与作用示意图 (选择1-2个最突出场景)**

  * **对应章节：** **「第二部分: 参赛项目介绍 - 一、项目概述 - (二) 应用场景」或 「二、解决方案 - (二) 技术路线 - 2.1 应用场景创新赋能」**
  * **图表内容：** **以某个具体应用场景（如工业物联网或车联网安全）为例，示意性地展示该场景原有的业务流程或系统架构，然后清晰标出CyberSec系统的核心组件（如安全芯片终端、安全网关、云平台服务）是如何嵌入其中，解决了哪些关键问题，并带来了哪些价值。**
  * **模板建议：** **简化的解决方案部署图或用例图**。可以有背景示意场景，然后叠加本系统的组件。
  * **信心评估：** **对此图的价值和内容建议为** **中度推测 (30-60%)**，如果文字描述已足够清晰，可省略。

### 流程图设计通用建议：

* **风格统一：** **所有流程图在颜色方案（推荐科技蓝、商务灰、深绿等专业色系）、字体（无衬线体，如微软雅黑、Arial）、形状（圆角矩形为主）、箭头样式等方面保持高度一致。**
* **简洁明了：** **图中文字尽量精炼，突出关键词。详细解释放在图注或正文中。避免信息过载。**
* **专业美观：** **注意对齐、间距、平衡，确保图表整体视觉效果专业、整洁。**
* **与文本呼应：** **流程图应与其在申报书中所处位置的文本内容紧密相关，互为补充。**
* **数量适中：** **一般来说，一份项目申报书中，3-5个高质量的核心流程图/架构图效果最佳。过多反而可能分散评委注意力。**

---


## 专业级HTML动态流程图绘制指令 (Professional Prompt for Dynamic HTML Flowchart Generation)

**[用户请在下方“第一部分”中填写您流程图的具体需求]**

### 第一部分：流程图内容与风格描述 (Flowchart Content & Style Description)

**1.1. 流程图总体目标与标题 (Overall Goal & Title of the Flowchart):**

* 例如: "绘制‘CyberSec核心技术赋能数据要素化与价值创造流程图’，旨在清晰展示原始数据经过核心技术赋能后形成数据要素并最终创造价值的完整过程。"
* 您的描述: **_________________________________________**

**1.2. 主要阶段/节点定义 (Main Stages/Nodes Definition):**

* 请逐个列出流程图中的主要阶段或节点。
* 对于每个阶段/节点，请提供：
* **唯一ID (Unique ID):** **(用于JS连接，例如** **rawDataStage**, **techBlock1** **等)**
* **标题 (Title):** **(显示在阶段/节点上的名称)**
* **核心内容 (Key Content):** **(例如，简短描述、关键特性、功能点列表等，建议使用项目符号** *** 项目点1**)
* 示例:
* ID: **stageOne**, Title: "数据采集", Content: *** 安全终端部署 * 合法/真实/完整性保障**
* 您的描述:

- ID: ____, Title: "____", Content: * ____ * ____ - ID: ____, Title: "____", Content: * ____ * ____ - ... (依此类推)

**1.3. 连接关系与逻辑流 (Connections & Logical Flow):**

* 请描述阶段/节点之间的连接。
* 对于每个连接，请说明：
* **源节点ID (Source Node ID):**
* **目标节点ID (Target Node ID):**
* **连接线样式 (Line Style):** **(例如:** **实线 (solid)**, **虚线 (dashed)**, **粗虚线 (thick-dashed)**)
* **箭头方向 (Arrow Direction):** **(例如:** **单向 (uni-directional) source指向target**, **双向 (bi-directional)**)
* **(可选) 连接线旁的标注文本 (Optional Label on Connector):**
* 示例:
* Source: **stageOne**, Target: **stageTwo**, Style: **solid**, Direction: **uni-directional**, Label: **(可选)**
* 您的描述:

- Source: ____, Target: ____, Style: ____, Direction: ____, Label (optional): "____" - Source: ____, Target: ____, Style: ____, Direction: ____, Label (optional): "____" - ... (依此类推)

**1.4. 总体布局偏好 (Overall Layout Preference):**

* 例如: "水平线性流程 (Horizontal Linear Flow)", "垂直层级结构 (Vertical Hierarchical Structure)", "中心辐射型 (Hub-and-Spoke)" 等。
* 您的描述: **_________________________________________**

**1.5. 期望视觉风格与特质 (Desired Visual Style & Qualities):**

* **关键词 (Keywords):** **例如:** **学术化 (academic)**, **严谨 (rigorous)**, **专业 (professional)**, **简洁清晰 (clean and clear)**, **视觉平衡 (visually balanced)**。
* **颜色偏好 (Color Palette Preference):** **(例如: "使用科技蓝、商务灰为主色调", "参考之前提供的[某图]的配色方案", "请AI推荐一套专业且适合学术报告的配色方案")**
* **字体偏好 (Font Preference - 可选):** **(例如: "无衬线字体，如Arial, Microsoft YaHei")**
* 您的描述: **_________________________________________**

**1.6. (可选) 容器大致尺寸预期 (Optional - Approximate Container Size Expectation):**

* 例如: **宽度约1200px，高度根据内容自适应**
* 您的描述: **_________________________________________**

---

### 第二部分：技术实现与代码规范要求 (Technical Implementation & Code Specifications)

**请AI严格遵循以下技术方案生成代码：**

**2.1. 输出格式 (Output Format):**

* **单一HTML文件:** **所有HTML结构、CSS样式（在**`<style>`**标签内）和JavaScript代码（在**`<script>`**标签内）需整合在同一个** **.html** **文件中，确保可直接在现代浏览器中运行。**

**2.2. HTML 结构 (HTML Structure):**

* **主容器 (Main Container):** **一个外层** **div** **作为流程图的总容器 (例如:** **class="flowchart-main-container"**), 并赋予其唯一ID (例如: **id="myFlowchartContainer"**)。
* **阶段/节点元素 (Stage/Node Elements):**
* 每个主要阶段/节点使用一个 **div** **元素表示 (例如:** **class="flow-stage"** **或** **class="node-block"**), 并必须拥有在 **1.2** **中定义的唯一** **id**。
* 阶段/节点内部结构：
* 标题使用 **h4** **或** **div** **(例如:** **class="stage-title"**).
* 内容列表使用 **ul** **和** **li** **(例如:** **class="stage-content-list"**).

**2.3. CSS 样式 (CSS Styling):**

* **布局 (Layout):**
* 主要使用 **Flexbox** **进行阶段/节点的布局 (例如，对于水平流程，主容器** **display: flex; justify-content: space-between;**)。
* 确保阶段/节点具有相同的外部高度，使用 **align-items: stretch;** **在父flex容器上。**
* **垂直内容填充:** **对于等高的flex子项（阶段/节点），其内部内容（如图文混排、列表）应能良好地垂直分布或居中，避免大量底部空白。这通常通过在子项内部也使用flex布局实现 (例如，阶段** **div** **设置为** **display: flex; flex-direction: column;**，其内容块 **flex-grow: 1; display: flex; justify-content: center; align-items: center;** **或** **justify-content: center;** **来垂直居中其子元素** **ul**)。
* **视觉风格 (Visual Style):**
* **专业色板:** **应用** **1.5** **中描述的颜色偏好或推荐一套。**
* **阶段/节点样式:** **定义边框 (颜色, 粗细,** **border-radius**), 背景色, 内边距 (**padding**), 外边距 (**margin**) 或间隙 (**gap** **for flex container), 以及细微的阴影 (**box-shadow**) 以增加层次感。**
* **文本样式:** **清晰的字体、合适的字号、行高、文本颜色。标题应比内容文本更突出。**
* **列表样式:** **移除默认列表符号，可使用自定义符号 (例如** **content: "▪";** **伪元素) 或无符号。**
* **容器样式:** **设定背景色、边框、内边距。**

**2.4. SVG 图形 (SVG Graphics):**

* **SVG画布 (SVG Canvas):** **在主容器内、所有HTML阶段/节点元素之上（通过** **z-index**）绝对定位一个SVG层 (例如: **`<svg class="connector-canvas">`**)，使其覆盖整个主容器区域。
* **交互穿透:** **设置** **pointer-events: none;** **在SVG画布上，允许鼠标事件穿透到下方的HTML元素。**
* **箭头定义 (**`<defs>`**):** **在SVG的** **`<defs>`** **部分定义可重用的箭头样式 (**`<marker>`**)。**
* 至少定义一个标准的单向箭头 (例如: **id="defaultArrowhead"**).
* (可选) 定义用于双向箭头的起始端反向箭头 (例如: **id="bidirectionalStartArrowhead"**).
* 箭头应包含 **viewBox**, **refX**, **refY**, **markerUnits**, **markerWidth**, **markerHeight**, **orient="auto-start-reverse"** **(或** **auto** **for start markers) 属性。箭头形状使用** **`<path>`**。
* **连接线:** **连接线本身将由JavaScript动态生成和添加。静态SVG部分主要包含**`<defs>`**。**

**2.5. JavaScript 动态连接 (JavaScript for Dynamic Connections):**

* **执行时机:** **脚本应在** **window.addEventListener('load', () => { ... });** **或类似事件后执行，确保所有HTML元素已加载并渲染完毕。**
* **核心功能函数:**
* **getPortPosition(elementId, portSide, relativeToContainer, offsetPercentage = 0.5, edgeOverlap = 0)**:
* **elementId**: 目标HTML元素的ID。
* **portSide**: 字符串，如 **'left'**, **'right'**, **'top'**, **'**bottom**'**，表示连接点在元素的哪条边。
* **relativeToContainer**: 主流程图容器的DOM元素，用于计算相对坐标。
* **offsetPercentage**: (可选, 默认0.5) 沿边的偏移百分比 (0.0为起点, 0.5为中点, 1.0为终点)。
* **edgeOverlap**: (可选, 默认0) 像素值，用于控制连接线端点“延伸入”或“退后于”元素边界的距离。正值表示延伸入元素内部，负值表示在边界外。
* 函数返回 **{x, y}** **对象，包含相对于** **relativeToContainer** **的SVG坐标。**
* **drawLine(p1, p2, options = {})**:
* **p1**, **p2**: **{x, y}** **对象，分别表示线的起点和终点坐标。**
* **options**: (可选) 对象，包含:
* **stroke**: 线条颜色 (默认: **#555** **或主题色)。**
* **strokeWidth**: 线条宽度 (默认: **2px** **或** **1.5px**)。
* **markerEndId**: 应用于线条末端的marker ID (例如: **defaultArrowhead**)。
* **markerStartId**: (可选) 应用于线条始端的marker ID (用于双向箭头)。
* **dashArray**: (可选) SVG **stroke-dasharray** **值 (例如:** **'5 3'** **用于虚线)。**
* **id**: (可选) 为SVG line元素设置id。
* 函数动态创建SVG **`<line>`** **(或根据需要创建** **`<path>`**) 元素并添加到SVG画布。
* **连接逻辑:**
* 遍历 **1.3** **中定义的连接关系。**
* 对每个连接，使用 **getPortPosition** **计算源节点和目标节点的准确连接点坐标。**
* 使用 **drawLine** **函数绘制连接线，并根据描述应用样式（实线/虚线、箭头）。**
* **确保主连接线（例如阶段之间的主流程线）具有足够的视觉突出度 (如通过** **edgeOverlap** **和** **strokeWidth** **控制)。**
