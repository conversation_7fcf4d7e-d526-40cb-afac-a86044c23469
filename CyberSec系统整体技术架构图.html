<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CyberSec 系统整体技术架构图 (优化箭头逻辑 - Refined)</title>
    <style>
        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f4f7f6;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: auto; /* Added for very tall diagrams if needed */
        }

        .flowchart-container {
            width: 900px; 
            /* Adjusted min-height if content makes it taller naturally */
            /* min-height: 720px;  */
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            position: relative; 
            padding: 20px;
            /* overflow: auto; */ /* Removed from container, body handles scroll */
        }

        .layer {
            border: 2px dashed #ccc;
            border-radius: 6px;
            padding: 20px 15px 15px 15px; 
            margin-bottom: 30px; 
            position: relative; 
        }
        .layer-title {
            position: absolute;
            top: -12px; 
            left: 20px;
            background-color: #ffffff; 
            padding: 0 10px;
            font-weight: bold;
            color: #555;
            font-size: 16px;
        }

        .cloud-layer { border-color: #87CEEB; background-color: rgba(135, 206, 235, 0.05); }
        .cloud-layer .layer-title { color: #4682B4; }

        .pipe-layer { border-color: #90EE90; background-color: rgba(144, 238, 144, 0.05); }
        .pipe-layer .layer-title { color: #3CB371; }

        .end-layer { border-color: #FFDAB9; background-color: rgba(255, 218, 185, 0.05); margin-bottom: 15px;}
        .end-layer .layer-title { color: #FFA07A; }
        
        .components-grid {
            display: flex;
            justify-content: space-around; 
            align-items: stretch; 
            gap: 15px; 
            min-height: 120px; 
        }

        .component {
            background-color: #fdfdfd; 
            border: 1px solid #ced4da;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            font-size: 13px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            flex: 1; 
            min-width: 150px; /* Ensure components have enough width */
            max-width: 280px; /* Prevent components from becoming too wide */
        }
        .component .name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
            font-size: 14px;
        }
        .component .detail {
            font-size: 11px;
            color: #666;
            line-height: 1.3;
        }
        
        #sct { border-left: 4px solid #FFA07A; } 
        #gscpsPipe { border-left: 4px solid #3CB371; } 
        #bsa { border-left: 4px solid #4682B4; } 

        .connector-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none; 
            z-index: 10; 
        }
        .line-label {
            font-size: 9px; /* Kept small to reduce clutter, can be increased */
            pointer-events: auto; 
            /* fill is set by JS */
        }
    </style>
</head>
<body>
    <div class="flowchart-container" id="flowchartContainer">
        <!-- 云端/中心：智能治理与服务层 -->
        <div class="layer cloud-layer" id="cloudLayer">
            <div class="layer-title">云端/中心：智能治理与服务层</div>
            <div class="components-grid">
                <div class="component" id="bsa">
                    <div class="name">区块链智能审计中台</div>
                    <div class="detail">数据权责界定<br>行为可追溯、透明监管</div>
                </div>
                <div class="component" id="dsgc">
                    <div class="name">数据安全治理中心</div>
                    <div class="detail">策略管理、元数据<br>自动化合规赋能</div>
                </div>
                <div class="component" id="pcde">
                    <div class="name">隐私计算与数据服务引擎</div>
                    <div class="detail">联邦学习、同态加密<br>数据可用不可见</div>
                </div>
            </div>
        </div>

        <!-- 管侧/网络：安全传输与防护层 -->
        <div class="layer pipe-layer" id="pipeLayer">
            <div class="layer-title">管侧/网络：安全传输与防护层</div>
            <div class="components-grid">
                <div class="component" id="gscpsPipe">
                    <div class="name">国密安全通信协议栈</div>
                    <div class="detail">身份认证、密钥协商<br>数据加密、完整性保护</div>
                </div>
                <div class="component" id="ismg">
                    <div class="name">智能安全管控网关</div>
                    <div class="detail">边界防护、DPI、IDS/IPS<br>国密VPN、威胁过滤</div>
                </div>
                <div class="component" id="tvpn">
                    <div class="name">可信VPN/专网通道</div>
                    <div class="detail">基于国密的安全隧道<br>跨网/跨域安全传输</div>
                </div>
            </div>
        </div>

        <!-- 端侧/边缘：硬件信任与执行层 -->
        <div class="layer end-layer" id="endLayer">
            <div class="layer-title">端侧/边缘：硬件信任与执行层</div>
            <div class="components-grid">
                <div class="component" id="sct">
                    <div class="name">自主可控安全计算终端</div>
                    <div class="detail">搭载"赛安"芯片<br>物理不可破、硬件信任根</div>
                </div>
                <div class="component" id="tee">
                    <div class="name">硬件级可信执行环境 (TEE)</div>
                    <div class="detail">硬件隔离执行<br>原始数据不出域</div>
                </div>
                <div class="component" id="lsosm">
                    <div class="name">轻量化安全OS与国密模块</div>
                    <div class="detail">安全启动、权限控制<br>接口安全、密码服务</div>
                </div>
            </div>
        </div>

        <svg class="connector-canvas" id="connectorCanvas">
            <defs>
                <marker id="arrowhead-solid-default" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#555" />
                </marker>
                <marker id="arrowhead-dashed-audit" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#28a745" />
                </marker>
                 <marker id="arrowhead-dashed-policy" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#007bff" />
                </marker>
                <marker id="arrowhead-dashed-trust" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#fd7e14" />
                </marker>
                <marker id="arrowhead-dashed-privacy" viewBox="0 0 10 10" refX="9" refY="5" markerUnits="strokeWidth" markerWidth="8" markerHeight="6" orient="auto-start-reverse">
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="#6f42c1" />
                </marker>
            </defs>
            <!-- Lines will be drawn here by JavaScript -->
        </svg>
    </div>

<script>
    window.addEventListener('load', () => {
        const svg = document.getElementById('connectorCanvas');
        const container = document.getElementById('flowchartContainer');

        // Helper function to get port coordinates
        function getPortPosition(elementId, side, offsetPercentage = 0.5, edgeOverlap = 0) {
            const elem = document.getElementById(elementId);
            if (!elem) { 
                console.error("Element not found for getPortPosition:", elementId); 
                return {x:0, y:0}; // Return a default to prevent further errors
            }
            const elemRect = elem.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();

            let x, y;

            const relativeTop = elemRect.top - containerRect.top;
            const relativeLeft = elemRect.left - containerRect.left;
            
            switch (side) {
                case 'top':
                    x = relativeLeft + elemRect.width * offsetPercentage;
                    y = relativeTop - edgeOverlap;
                    break;
                case 'bottom':
                    x = relativeLeft + elemRect.width * offsetPercentage;
                    y = relativeTop + elemRect.height + edgeOverlap;
                    break;
                case 'left':
                    x = relativeLeft - edgeOverlap;
                    y = relativeTop + elemRect.height * offsetPercentage;
                    break;
                case 'right':
                    x = relativeLeft + elemRect.width + edgeOverlap;
                    y = relativeTop + elemRect.height * offsetPercentage;
                    break;
                default: // center
                    x = relativeLeft + elemRect.width / 2;
                    y = relativeTop + elemRect.height / 2;
            }
            return { x, y };
        }

        // Helper function to draw a line with an optional label
        function drawLineWithLabel(p1, p2, options = {}) {
            if (!p1 || !p2) {
                 console.error("Invalid points for drawLineWithLabel:", p1, p2, options.label);
                 return;
            }
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', p1.x);
            line.setAttribute('y1', p1.y);
            line.setAttribute('x2', p2.x);
            line.setAttribute('y2', p2.y);
            line.setAttribute('stroke', options.color || '#555');
            line.setAttribute('stroke-width', options.strokeWidth || '1.5');
            if (options.dashArray) {
                line.setAttribute('stroke-dasharray', options.dashArray);
            }
            if (options.markerEnd) {
                line.setAttribute('marker-end', `url(#${options.markerEnd})`);
            }
            if (options.id) {
                line.id = options.id;
            }
            svg.appendChild(line);

            if (options.label) {
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                // Position label near the midpoint of the line
                let labelX = (p1.x + p2.x) / 2 + (options.labelOffsetX || 0);
                let labelY = (p1.y + p2.y) / 2 + (options.labelOffsetY || -5); // Default slightly above line

                text.setAttribute('x', labelX);
                text.setAttribute('y', labelY);
                text.setAttribute('fill', options.labelColor || options.color || '#333');
                text.setAttribute('class', 'line-label');
                text.setAttribute('text-anchor', 'middle');
                text.textContent = options.label;
                svg.appendChild(text);
            }
        }
        
        const COLOR_SOLID_DEFAULT = '#555';
        const COLOR_AUDIT = '#28a745';        // Green
        const COLOR_POLICY = '#007bff';       // Blue
        const COLOR_TRUST = '#fd7e14';        // Orange
        const COLOR_PRIVACY = '#6f42c1';      // Purple

        // --- End-Layer Internal ---
        drawLineWithLabel(getPortPosition('sct', 'right', 0.4), getPortPosition('tee', 'left', 0.4), { markerEnd: 'arrowhead-solid-default', label: '提供硬件隔离/安全基座', labelOffsetY: -10, labelOffsetX:10 });
        drawLineWithLabel(getPortPosition('sct', 'right', 0.6), getPortPosition('lsosm', 'left', 0.6), { markerEnd: 'arrowhead-solid-default', label: '承载安全操作系统',labelOffsetX: -10, labelOffsetY: 12 });

        // --- End-Layer to Cloud (Audit to BSA) ---
        // Spaced out on BSA's bottom edge and SCT/TEE/LSOSM top edge
        drawLineWithLabel(getPortPosition('sct', 'top', 0.2), getPortPosition('bsa', 'bottom', 0.25, 2), { color: COLOR_AUDIT, dashArray: '4 2', markerEnd: 'arrowhead-dashed-audit', label: '操作日志', labelOffsetX: -20, labelOffsetY: -8 });
        drawLineWithLabel(getPortPosition('tee', 'top', 0.5), getPortPosition('bsa', 'bottom', 0.5, 2), { color: COLOR_AUDIT, dashArray: '4 2', markerEnd: 'arrowhead-dashed-audit', label: '安全事件', labelOffsetY: -8 });
        drawLineWithLabel(getPortPosition('lsosm', 'top', 0.8), getPortPosition('bsa', 'bottom', 0.75, 2), { color: COLOR_AUDIT, dashArray: '4 2', markerEnd: 'arrowhead-dashed-audit', label: '模块日志', labelOffsetX: 20, labelOffsetY: -8 });

        // --- End-Layer TEE to Cloud Privacy (PCDE) ---
        drawLineWithLabel(getPortPosition('tee', 'top', 0.8), getPortPosition('pcde', 'bottom', 0.2, 2), { color: COLOR_PRIVACY, dashArray: '5 3', markerEnd: 'arrowhead-dashed-privacy', label: '本地安全处理', labelOffsetX: 30, labelOffsetY: -10 });

        // --- Pipe-Layer Internal ---
        drawLineWithLabel(getPortPosition('gscpsPipe', 'right', 0.3), getPortPosition('ismg', 'left', 0.3), { markerEnd: 'arrowhead-solid-default', label: '协议支持', labelOffsetY: -10 });
        drawLineWithLabel(getPortPosition('gscpsPipe', 'right', 0.7), getPortPosition('tvpn', 'left', 0.7), { markerEnd: 'arrowhead-solid-default', label: '协议支持', labelOffsetY: 12 });

        // --- Pipe-Layer to Cloud (Audit to BSA) ---
        // Spaced out
        drawLineWithLabel(getPortPosition('ismg', 'top', 0.25), getPortPosition('bsa', 'bottom', 0.4, 2), { color: COLOR_AUDIT, dashArray: '4 2', markerEnd: 'arrowhead-dashed-audit', label: '网络行为日志', labelOffsetX: -30, labelOffsetY: -8});
        drawLineWithLabel(getPortPosition('tvpn', 'top', 0.75), getPortPosition('bsa', 'bottom', 0.6, 2), { color: COLOR_AUDIT, dashArray: '4 2', markerEnd: 'arrowhead-dashed-audit', label: '隧道审计', labelOffsetX: 25, labelOffsetY: -8});
        
        // --- Cloud-Layer DSGC Interactions & Control Downwards ---
        // DSGC to ISMG (Policy Down)
        drawLineWithLabel(getPortPosition('dsgc', 'bottom', 0.5), getPortPosition('ismg', 'top', 0.5), { color: COLOR_POLICY, dashArray: '5 3', markerEnd: 'arrowhead-dashed-policy', label: '安全策略下发', labelOffsetY: 12 });
        // DSGC to PCDE (Policy) - more horizontal
        drawLineWithLabel(getPortPosition('dsgc', 'right', 0.5), getPortPosition('pcde', 'left', 0.5), { color: COLOR_POLICY, dashArray: '5 3', markerEnd: 'arrowhead-dashed-policy', label: '数据使用策略', labelOffsetY: -10 });
        // DSGC to BSA (Policy/Config) - more horizontal
        drawLineWithLabel(getPortPosition('dsgc', 'left', 0.5), getPortPosition('bsa', 'right', 0.5), { color: COLOR_POLICY, dashArray: '5 3', markerEnd: 'arrowhead-dashed-policy', label: '治理/审计规则', labelOffsetY: -10 });

        // --- Data/Status Upwards from Pipe to DSGC ---
        drawLineWithLabel(getPortPosition('ismg', 'top', 0.75), getPortPosition('dsgc', 'bottom', 0.25, 2), { color: COLOR_TRUST, dashArray: '4 2', markerEnd: 'arrowhead-dashed-trust', label: '设备状态/态势', labelOffsetY: 12, labelOffsetX: 35 });

        // --- General Trust Up Flow (SCT -> DSGC) ---
        drawLineWithLabel(getPortPosition('sct', 'top', 0.1), getPortPosition('dsgc', 'bottom', 0.8, 2), { color: COLOR_TRUST, dashArray: '6 3', markerEnd: 'arrowhead-dashed-trust', label: '硬件信任状态', labelOffsetX: -40, labelOffsetY: 12 });
        
        // --- General Policy Down Flow (ISMG -> LSOSM, representing policy propagation to edge OS) ---
        drawLineWithLabel(getPortPosition('ismg', 'bottom', 0.8), getPortPosition('lsosm', 'top', 0.2), { color: COLOR_POLICY, dashArray: '6 3', markerEnd: 'arrowhead-dashed-policy', label: '安全配置下发', labelOffsetX: 40, labelOffsetY: 12 });

        // --- "Original" data flows, with refined placement ---
        // SCT to ISMG (Data Flow)
        drawLineWithLabel(getPortPosition('sct', 'top', 0.6), getPortPosition('ismg', 'bottom', 0.2, 2), { markerEnd: 'arrowhead-solid-default', label: '原始数据流', labelOffsetY:12, labelOffsetX: -45 });
        // LSOSM to TVPN (Data Flow)
        drawLineWithLabel(getPortPosition('lsosm', 'top', 0.5), getPortPosition('tvpn', 'bottom', 0.5), { markerEnd: 'arrowhead-solid-default', label: '加密数据流', labelOffsetY:12 });
        // TVPN to PCDE (Secure Channel to Privacy)
        drawLineWithLabel(getPortPosition('tvpn', 'top', 0.5), getPortPosition('pcde', 'bottom', 0.8, 2), { dashArray: '4 2', markerEnd: 'arrowhead-dashed-privacy', label: '安全通道', labelOffsetY:-10, color: COLOR_PRIVACY, labelOffsetX:15 });

    });
</script>

</body>
</html>