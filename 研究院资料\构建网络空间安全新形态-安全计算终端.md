# 安全计算终端：构建网络空间安全新形态 - 西交网络空间安全研究院 (2024年10月)

## 目录 (CONTENTS)

* 现状和安全管控体系
* 核心技术积累
* 安全计算终端

---

## 一、现状：基于边界的网络安全防护

**(图示说明：一个复杂的网络拓扑图，展示了“基于边界的网络安全防护”的典型场景)**

***核心要素：**
    *   中央是连接到 **Internet** 的设备，部署了**防火墙、入侵检测、安全审计**。这构成了“**传统边界**”。
***外围连接与应用：**
    *   远程监测互联
    *   万物互联 (IoT)
    *   车联远控
    *   环境感知
    *   车况监测
    *   远程抄表
    *   远程检测
***突出问题：****问题频发!**

***思考：**
    *   现有攻击手段有哪些?
    *   防护手段有哪些?
    *   防护手段的共性问题和边界安全的缺陷在哪里?
    *   有没有新型的防护思路可以弥补缺陷?

---

## 二、现状：常见攻击方式

**(此页展示了多种常见的网络攻击方式及其案例)**

1.**中间人攻击 (MITM)**
    ***图示：** 用户(受攻击者) <-> 拦截、窃取 (攻击者) <-> 服务器
    ***案例：**
        *   乌克兰电网攻击事件
        *   OpenSSL的“心脏出血”漏洞
2.**0day/1day/ nday 攻击**
    ***图示：** 时间轴展示漏洞从公开到补丁发布再到仍被利用的过程。
        ***0day:** 漏洞未公开，无补丁。
        ***1day:** 漏洞公开披露后还没有修复补丁的漏洞。
        ***nday:** 厂商提供了修复补丁，但是仍然还在被利用的漏洞。
    ***0day攻击案例：**
        *   宝洁公司
        *   多伦多市政府
    ***嵌入式系统0day：**
        *   华为Eudemon企业级防火墙攻击，芯片ROM
3.**社会工程学攻击**
    ***图示：** 描述了DNS劫持或欺骗用户访问虚假网站的过程。
        *   攻击者设置虚假网站。
        *   用户被引导至虚假网站并输入信息。
    ***案例：**
        *5.4亿美金Skymavis 加密货币
        *   推特大规模账户被黑
4.**COTTONMOUTH-1**
    ***(图示：一个类似USB设备的硬件照片，型号为COTTONMOUTH-1)**
    ***单机木马攻击案例：**
        *   西安某医院
        *   “USBee” 软件 (可能指通过USB传播或利用USB接口的恶意软件)
5.**密码爆破攻击**
    ***图示：** 展示了尝试不同密码组合的界面。
    ***案例：**
        *   LinkedIn 1.67亿数据泄露
        *   信用报告机构 Equifax
6.**DDoS (分布式拒绝服务) 攻击**
    ***图示：** 攻击者(Client)控制多个主控程式(Handler)，进而控制大量攻击程式(Agent)向受攻击对象发起攻击。
    ***案例：**
        *   巴西多家银行和金融机构
        *   全球最大的代码托管平台 GitHub

---

## 三、现状：国内网络安全产业全景图

**(此页包含一个柱状图和一个饼图，分析国内网络安全产业情况)**

***左侧柱状图：各领域申报/收录情况对比**
    *   (由于OCR未能完全识别所有细分领域，此处省略具体排名，但图表显示了众多细分安全领域及其相对热度，如攻防演练、信创安全、安全意识与培训、渗透测试、安全运营态势感知等。)
    *   数量级从0到120+。

***右侧饼图：各领域营收占比及竞争热度**
    *   网络与通信安全: 20.6%
    *   网络安全服务: 12.3%
    *   应用与业务安全: 10.7%
    *   安全管理与运营: 8.4%
    *   计算环境安全: 7.3%
    *   数据安全: 7.1%
    *   密码技术及应用: 7.0%
    *   安全支撑技术与体系: 6.8%
    *   身份与访问安全: 5.6%
    *   云安全: 4.8%
    *   工业互联网安全: 4.1%
    *   物联网安全: 2.0%
    *   软件供应链安全: 1.6%
    *   移动安全: 1.2%
    *   物理环境安全: 0.4%

***行业数据：**
    *510家安全厂商
    *4941个产品
    *1051亿元规模

***行业问题总结：**
    1.  产品同质化严重，超过85%的产品集中在“边界防护”，涉及终端设备和通信通道的厂商和产品非常少，但目前影响恶劣事件一半以上产生于终端；
    2.  不管边界还是终端，实现方式几乎基于操作系统，软件方式实现；
    3.  产值不以技术为导向，厂商间“战国纷争”；
    4.  尚未出现一种可以实现多种防护手段的技术形式出现。

---

## 四、安全计算终端和安全通道

### 4.1 计算终端产品系统架构图

**(图示说明：一个详细的系统架构图，展示了安全计算终端的内部结构和接口)**

***核心组件：安全管控模组**
    *   审计存储
    *   电池
    *   网络接口 (旁路管控、北斗)
    *   USB/DVP 接口 (旁路管控)
    *4G/5G 模块
    *   连接到 **主CPU及系统**
***外部接口与设备：**
    *   带内/外监控
    *   认证 (指纹/动态密码等)
    *   用户鼠标/键盘/视频 (USB/DVP)
    *   USB PORT1, PORT2, 3, 4...
    *   USB B (外设)
    *   HU (可能是Hub或Head Unit)
    *   电源控制
***专用接口授权**
***两种体系：**
    *   飞腾 + 银河麒麟 信创体系
    *   WinTel 体系

### 4.2 安全功能点

* 内生TCM可信密码及启动检测
* 主板级隔离USB接口的病毒和木马传播
* 基于私有国密安全传输协议的网络协议管控
* 芯片级高限定电子围栏
* 安全管控系统不可更改、不可绕过
* 基于模型的自适应检测和主动防御
* 防拆机和硬盘数据自销毁策略

**(右下角有一张笔记本电脑形态的安全计算终端实物照片)**

---

## 五、整体应用框图

**(图示说明：展示了“赛安”系列产品在不同应用场景中的部署方式)**

***终端设备：**
    *   赛安安全电脑 (连接赛安一、二号芯片)
    *   应用于：
        *   医疗移动、远程会诊
        *   政务移动办公、政务远程处理
        *   重要行业和部门远程指挥、办公
***网络与平台：**
    *   赛安安全电脑通过 **安全通信** 连接到 **赛安安全网关** (内含赛安三号芯片)
    *   赛安安全网关通过 **安全管控** 连接到 **赛安安全管控平台**
    *   赛安安全管控平台连接到 **安全服务器**
***安全能力（由平台提供给各应用）：**
    *   安全加密
    *   多因素授权
    *   行为审计
    *   电子围栏
    *   ... (其他)
***应用场景：** 政务、司法、公安、医疗、金融等应用场景。

---

## 六、基于专用芯片的安全管控系统

***核心理念：** 研发物理层的网络安全管控芯片，构建一体化网络安全管控系统，为“云边端”内外网穿越，物联网安全终端的互联，建立安全通道。
***两大关键点：**
    1.**01:** 从硬件层面保护数据资源免于非授权访问、篡改。
    2.**02:** 建立芯片级安全通信专用通道，实现数据和通道两方面的安全管控。

### 6.1 基于专用芯片的安全管控体系细节

**(此页包含三个主要部分，阐述芯片级安全体系的构成)**

1.**芯片安全体系 (左侧，自下而上构建)：**
    *   CPU自主拓展安全管控指令集
    *   芯片硬件固有安全
    *   芯片固件安全策略
    *   可信嵌入式安全管控操作系统
    *   国密多场景网络传输协议
    *   安全多要素应用策略

2.**独有的固有安全存储机制 (中间)：**
    *   永久不可更改存贮空间
        *   高限制性写存贮区
        *   用户数据区
    *   硬件触发和芯片特权模式
    *   硬件OS自检策略
        *   CPU缓存安全区清除
        *   芯片数据安全通道格式
    ***密文运算速率2Gbps**

3.**创新性的功能区域划分 (右侧)：**
    *   安全管控功能区域
        *   业务区域
        *   安全固件及策略区域
    *   硬件实现真随机发生及算法加速
        *   芯片动态真随机发生器
        *   国密算法硬件加速
        *   现场可编程状态机和数据直接搬运

***管控芯片型号与规格：**
    *   管控芯片V1: 800MHZ 单核 /40nm
    *   管控芯片V2: 1GHZ 双核 /40nm
    *   管控芯片V2P: 1.2GHZ 4核 /28nm

---

## （ повторный 目 录 ）

**(页面P10与P2内容基本一致，为目录，指向“网络空间安全现状”、“核心技术积累”、“安全计算终端”。此处略去重复内容，标记为重复目录)**

* 网络空间安全现状 (已在前述内容中覆盖)
  ***核心技术积累** (接下来的内容)
* 安全计算终端 (已在前述内容中部分覆盖，后续会有更详细的特征介绍)

---

## 七、研究院简介

**(此页包含一张城市风景/园区图片和文字介绍)**

* 西交网络空间安全研究院由诸暨市人民政府与西安交通大学共建。
* 中国科学院院士、西安交通大学电子与信息学部主任管晓宏担任院长和首席科学家，面向网络与数据安全领域科技发展前沿，集聚高层次研发管理人才，实施科技成果转移转化。
* 研发网络安全自主可控专用芯片、软硬结合内外网安全管控系统核心技术，提供行业安全管控整体解决方案，建设安全专用芯片研究中心、安全管控系统研究中心、行业解决方案三个研究中心，及创新创业服务平台、合作交流服务平台两个服务平台。

---

## 八、研究院院长、首席科学家：管晓宏院士

**(此页为管晓宏院士的个人照片及简介)**

***管晓宏院士**
***荣誉与资质：**
    *   教育部先进集体、黄大年式教师团队负责人
    *   清华大学本科、硕士，美国康涅狄格大学博士，哈佛大学访问科学家
    *   中国科学院院士、IEEE Fellow
    *   西安交通大学电子与信息学部主任、教授
    *   智能网络与网络安全教育部重点实验室首席科学家
    *   国家自然科学基金创新群体负责人
    *   主持国家自然科学二等奖2项
    *   何梁何利科学与技术进步奖
    *   美国李氏基金杰出成就奖等国际奖励

---

## 九、核心技术一：安全管控芯片介绍 (赛安二号)

**(此页为安全管控芯片“赛安二号”的详细框图和特性说明)**

***芯片框图 (非常详细，包含众多模块):**
    ***主要处理器核心：**
        *   ARM® Cortex™ Quad-Core A53 (32KB/32KB SCU/NEON, 512KB L2 Cache)
        *   ARM® Cortex™ Single-Core M4F (Thumb-2/FPU)
    ***接口与模块 (部分列举)：** Emulator Port, JTAG, UART, PCIE, SATA, USB3, HSIC/SS IC, SDIO, PCM/I2S, I2C, M.2接口, PMU (Charger, Battery, Power ON/OFF, Reset, RTC), PWRC, TIMER, WDT, GIC, MailBox, BP147, TPZCTL, DMAC/DMAG, CPHER, Sec_Memory, SRAM, CEC_BOOT_ROM, SECURITY (SM2/SM3/SM4, SM1, SSLx2, Ter Sensor), Ethernet MAC x2, AP INC429, CAN x2, PWM x4, RS232/RS485, LCDC, GPIO, SDIO_S, SDMMC, MEMCTL 64 bit, QSPI, FSMC, KEYPAD 5\*5, DDR4, FLASH, Memory.
    ***硬件触发及芯片特权模式**
    ***功能区域划分：业务区域、管控区域**

***主要特性：**
    ***高处理性能：**
        *4核 Cortex-A53, 1.2GHz
        *32KB ICache 及 32KB DCache (应为DCache)
        *512KB L2 Cache
        *   单核 Cortex-M4F
    ***高密文数据吞吐 (硬件实现真随机发生及算法加速)：**
        *   算法硬件加速，1Gbps (256Mbps x 4)
        *   EtherNet、USB、PCIE等高速输入输出
        *   现场可编程状态机和数据直接搬运
    ***芯片安全体系 (独有的固有安全存储机制)：**
        *   RTC、白名单等“不可更改”
        *   CPU、总线、中断、内存等模块安全访问“不可绕过”
        *   锁定JTAG调试和efuse检测模式

---

## 十、核心技术二：可信安全管控操作系统

***核心思想：** 在CPU上同时隔离运行非安全核 (任务核) 和安全核 (管控核)。
    ***普通世界 (Non-Secure World)：** 代码在非安全核上运行，只能访问普通世界的硬件资源，运行通用操作系统，主要负责普通应用任务的执行。
    ***安全世界 (Secure World)：** 代码在安全核上运行，能够访问所有硬件资源，运行安全操作系统，主要负责管控任务的运行。
***(图示：一个简化的模型图，展示了“监控模式”下，“普通世界”运行“通用操作系统”和“普通应用APP”，而“安全世界”运行“安全操作系统”（内含“安全内核”）和“安全应用SAPP”。两者隔离。)**
***关键特性：**
    *   可信操作系统与通用操作系统的切换
    *   私有审计存储开发
    *   专用接口授权
    *5W安全管控开发多因素策略授权 (5W可能指Who, When, Where, What, Which)
    *   电源管控方案

---

## 十一、核心技术三：安全管控私有传输协议

***协议定义：** 基于Diffie-Hellman密钥交换的轻量级传输层安全加密协议。支持多种加密算法，并独特支持国密算法，提供完整的加密通信、身份验证与完整性保护。
***(图示：Diffie-Hellman (DH) 密钥交换过程示意图)**
    *   Alice: Private key (dA), Public key (QA = dA * G)
    *   Bob: Private key (dB), Public key (QB = dB * G)
    *   共享密钥: Share = dA * QB = dB * QA = dA * dB * G
***特点：**
    *   集成SM2, SM3, SM4国密算法
    *   模块化的协议框架
    *   可构建定制化的安全通信方案
    *   双向DH密钥交换机制
    *   支持多种不同类型DH函数
    *   多种不同类型的加密算法哈希函数

---

## 十二、核心技术四：安全嵌入式系统固件

***核心能力：支撑实现5个维度安全体系 (5W1H框架)**
    ***(图示：一个横向的流程图，展示了策略如何通过5W1H维度进行安全控制)**
    ***策略 (贯穿始终)**
    ***Who (访问者)：** 身份/业务权限/生理特征等认证、授权。
    ***When (访问时间)：** 独立时钟及电源控制。防止非授权。
    ***Where (访问地点)：** 独立电池及北斗定位。防止非授权或者意外 (盗抢丢等)。
    ***Which (访问方式/对象)：**
        1.  有线/WiFi/4G/5G/北斗等
        2.  地址、端口
        3.  明文/加密/签名/防伪等
    ***What (访问行为/内容)：**
        ***本地：** 芯片内记录屏幕、键盘、使用人等活动。事后审计 (是否违规等)。
        ***网关：** 网关进行应用层业务行为审计。

---

## 十三、核心技术五：基于AI的网络防御应用

***行为分析：** 静态文件AI、动态行为AI行为关联分析，检测和防护勒索病毒、漏洞利用，发现异常行为，识别恶意活动或未知威胁。
***安全运营：** 实时监控网络流量、日志和安全事件。自动化渗透测试，提前防御潜在的攻击。
***特征库：** 自动提取病毒的特征，降低特征库更新频率，提高安全防护的灵活性。
***大语言模型的应用：** 应对网络钓鱼检测、恶意软件检测、人工智能深度伪造、自动化社会工程攻击等。

---

## 十四、核心技术六：安全管控平台

***基于AI的多因素策略授权/审计 (2/3/4人等)**
    ***(图示：多个人形图标代表多因素制约)**
***根据业务岗位需求，确定安全策略**
    ***(图示：指纹、口令、令牌图标汇聚到“多因素授权”，然后指向“多因素审计”)**
***(图示：一个简化的存储示意图，显示了“高限制性写存贮区”、“高安全可读写存储区”和“永久不可更改存贮空间”。右侧有一个更详细的存储区域划分图，标有Key以外锁定、Key锁定，以及KEY 0, KEY 1, KEY 2, chip ID等区域，并有地址范围000-03F。)**
***核心优势：**
    *   内部人员、操作系统无法更改安全策略及管控固件。
    *   软硬绑定，专业人员无法镜像拷贝、逆向分析。
    *   避免各种漏洞及人为风险。

---

## （再次重复的目录）

**(页面P19与P2, P10内容基本一致，为目录，指向“网络空间安全现状”、“核心技术积累”、“安全计算终端”。此处略去重复内容。)**

* 网络空间安全现状 (已覆盖)
* 核心技术积累 (已覆盖)
  ***安全计算终端** (接下来的内容将更侧重其安全特性)

---

## 十五、安全计算终端 - 安全特征

**(此页详细列举了安全计算终端的六大安全特征)**

1.**内生TCM可信密码及启动检测**
    *   安全启动检测: Boot检测硬件和操作系统是否有变更；
    *   认证与授权: 系统启动和应用启动进行认证；
    *   数据加密和解密: 采用硬件级加解密；
    *   建立计算平台的安全信任根基: 提供密钥保护等安全信任功能。
2.**主板级隔离USB接口的病毒和木马传播**
    *   采用物理隔离式方法，克服了普通电脑内部数据保护系统单纯在软件层次上保护数据所存在的安全缺陷，实现了硬件级的USB接口安全访问；
    *   有效防止病毒木马入侵，防止电脑中的数据的内部泄漏、外部扩散，木马窃取等，实现了USB接口的自主防护。
3.**基于私有国密安全传输协议的网络协议管控**
    *   数据加密: 确保数据在传输过程中不被窃取或篡改；
    *   身份验证: 确保发送者的身份是可信的，防止伪造；
    *   完整性保护: 通过使用哈希函数，确保数据在传输过程中未被修改；
    *   安全隧道: 在公共网络上创建一个安全的“隧道”，允许用户在不安全的网络中进行安全通信。
4.**芯片级高限定电子围栏**
    *   相对传统的电子围栏白名单实现了硬件化配置，通过芯片内部不同种类的存储机制和存储介质，保证白名单的安全；
    *   只有特殊权限的管理人员使用私有硬件接口才能添加和修改。
5.**安全管控系统不可更改、不可绕过**
    *   计算终端处理的数据业务、操作命令以及键鼠、外设等接口，须经由安全管控系统的判断和处理才能放行；
    *   安全管控系统基于团队自主研发的安全管控芯片，运行自主开发的嵌入式可信安全操作系统，在整体方案中配置不可更改、不可绕过。
6.**基于模型的自适应检测和主动防御**
    *   异常行为检测: 使用机器学习算法分析网络流量和用户行为，建立正常行为的基线模型，检测出异常行为的情况；
    *   实时数据分析: 通过AI分析实时数据流，识别潜在的安全威胁；
    *   智能日志分析: 利用自然语言处理技术分析安全日志，识别潜在的安全事件和攻击模式。

---

## 十六、内生TCM可信密码及启动检测详解

**(此页通过一个环形图和流程图详细解释了TCM安全启动机制)**

***环形图（围绕核心功能展开）：**
    ***安全启动检测：** 该功能能检查计算机在启动时，检查固件及操作系统等软件有没有被非法修改，确保系统从安全状态开始运行。比如可以检测到硬盘被替换并采取措施。
    ***硬件安全监控：** 该功能可以进行硬件级别的非法访问和篡改尝试并采取相应的安全措施。比如检测到硬盘被非法替换，可以对硬盘数据进行销毁等操作。
    ***认证与授权：** 在启动系统和应用程序时，该功能会确认用户的身份，确保只有被授权的人才能使用。
    ***数据加密和解密：** 该功能能使用硬件直接进行数据的加密和解密，保护信息不被未经授权的人读取。
    ***建立安全信任根基：** 通过提供密钥保护等功能，这个功能能确保计算平台的安全性，建立起一个可信赖的基础。

***内生TCM安全启动全流程 (右侧流程图)：**
    1.**准备工作：**
        *   产生一对SM2算法公钥 `pubkey` 和私钥 `prikey`。
        *   将公钥 `pubkey` HASH成唯一的摘要 (KEY)。
        *   将 KEY 一次性写入芯片内部的EFUSE密钥区。
        *   准备完整的二级boot数据二进制 (BIN)，将其HASH成唯一标识32Byte (BN)。
        *   将 BIN 用 `prikey` 签名，得到签名数据 (SIGN)。
        *   将 `pubkey` 和 SIGN 写入Flash的头部信息。
    2.**验证流程：**
        *   芯片启动读取EFUSE的密钥区的 KEY。
        *   BOOTROM读取Flash的 `pubkey`，将其HASH成唯一标识 (KEY1)。
        ***比较 KEY == KEY1?**
            *False: 程序退出。
            *True: 继续。
        *   BOOTROM读取Flash的 SIGN，利用公钥 `pubkey`。
        ***验签通过?**
            *False: 程序退出。
            *True: 程序成功加载。

---

## 十七、主板级隔离USB接口的病毒和木马传播

**(此页包含USB接口隔离的硬件实现、转发技术实现以及示意图)**

***USB接口隔离硬件实现：**
    *   主板上实现电路上USB接口硬件隔离。
    *   USB设备插入到安全管控芯片一侧，在安全管控芯片内，实现USB信号的转发。
    *   安全管控芯片内运行USB管控检测程序，监控木马等危险内容。
    *   安全管控芯片可硬件控制主机的USB电源开关，根据管控程序检测策略，选择开关主机侧的USB电源。

***USB转发技术实现：**
    *   在安全管控芯片使用 `f_mass_storage` 技术和检测程序来实现USB设备的转发，将文件系统或存储设备模拟成USB存储设备。

***示意图：**
    ***左侧：USB管控流程**
        *   USB设备 -> 插入 -> 管控芯片 (内含USB检测程序) -> USB安全转发 -> USB模块 (主机侧)
        *   管控芯片可以控制USB电源。
    ***右侧：USB硬件连接图 (简要)**
        *   安全管控处理模块连接到GPIO, USB管控, USB HUB (USB 2.0 HOST), USB 2.0 OTG, Micro USB。
        *   另一端连接到PC主机 (包含VGA, SATA, USB, HDMI, PCIE X16等接口)。
        *   USB设备最终连接到主机。

---

## 十八、基于私有国密安全传输协议的网络协议管控

***核心机制：** 芯片内部的数据传输严格使用对称加密的通道格式，与外部设备的通信，会建立起安全通信的VPN通信隧道，保证数据双向传输的安全性。使用的协议为定制化的私有国密安全传输协议，简化了通信的握手次数。
***优势：**
    ***√ 更安全：** 使用国密算法加解密。
    ***√ 更快速：** 使用硬件加密模块。
    ***√ 更可控：** 自主定制的传输协议。
***(图示：两个“芯片安全设备”通过各自的“网关”与“云服务器”进行“安全通道”通信的示意图。数据在发送前“密钥加密”，接收后“密钥解密”。)**
***除私有协议之外支持的协议：**
    *   √ IPSEC
    *   √ SSL VPN
    *   √ 普通模式

---

## 十九、芯片级高限定电子围栏

***存储配置：** 安全芯片内置了512K sec_EEPROM区域以及32MB sec_Prom用户区。
    1.  `sec_EEPROM`区域保存芯片内部配置参数，该区域拥有开放的读权限，但是只能通过特定专用板卡才能对该区域写入修改。该区域无法通过软件来修改。
    2.  特定硬件板卡有专用授权方案，只有板卡通过授权才能对芯片的 `sec_EEPROM`进行修改。芯片内置的写操作校验逻辑应满足“一芯一密”的标准。即每个芯片都有自己的密钥key，特定硬件板卡要修改 `sec_EEPROM`的内容，也必须通过指定芯片内部的唯一key的加解密，才能正确完成修改写入。
***(图示：一个PC机通过“专用配置接口”连接到“安全管控人员”使用的“配置专用工具”，该工具用于配置“管控芯片”。管控芯片内部有“永久不可更改存贮空间”，分为“高限制性写存贮区”和“用户数据区”。)**
***关键特性：**
    ***√ 基于高限制性写存贮区，可以写入电子围栏，保证5W的黑白名单的安全可控。** (5W可能指代Who, What, When, Where, Why/Which)
    ***√ 利用专用配置接口修改，只有专有人员才可修改其内容。**

---

## 二十、安全管控系统不可更改、不可绕过

* 计算终端处理的数据业务、操作命令以及键鼠、外设等接口，须经由安全管控系统的判断和处理才能放行；
* 安全管控系统基于团队自主研发的安全管控芯片，运行自主开发的嵌入式可信安全操作系统，在整体方案中配置不可更改、不可绕过。
  ***(图示：展示了两种不安全路径和一种安全路径)**
  ***不安全1 (不可直接访问):** 用户直接访问受保护资源 (被打叉)。
  ***不安全2 (不可绕过访问):** 用户绕过管控模块，键鼠外设操作直接访问受保护资源 (被打叉)。
  ***安全路径:** 用户 -> 键鼠外设操作 ->**管控模块** (包含硬件管控、安全系统不可更改) -> 操作转发 -> 受保护资源。

---

## 二十一、基于模型的自适应检测和主动防御

***AI网络防御：**
    ***行为分析：** 静态文件AI、动态行为AI行为关联分析。
    ***安全运营：** 实时监控网络流量、日志和安全事件。
    ***特征库：** 自动提取病毒的特征，降低特征库更新频率。
    ***大语言模型的应用：** 应对网络钓鱼检测、恶意软件检测。
    ***(图示：一个被保护的系统，外有“主动防御”和“自适应检测”层。攻击者通过“攻击手段”尝试攻击，被“AI防御手段”拦截。AI模型通过训练不断优化。)**
***AI网络攻击/代理：**
    *   人工智能代理结合了大型语言模型和自动化软件，可以通过阅读安全公告成功利用现实世界中的安全漏洞。
    *   研究AI代理实现迭代的网络安全攻防战。

---

## 二十二、嵌入式系统攻防和桌面级系统攻防技术积累

**(表格形式对比了多种攻击手段在普通设备和安全管控设备上的效果)**

| 现存攻击手段               | 原理实现         | 普通嵌入式设备 | 安全管控设备 | 现存攻击手段          | 原理实现           | 普通嵌入式设备 | 安全管控设备 |
| -------------------------- | ---------------- | -------------- | ------------ | --------------------- | ------------------ | -------------- | ------------ |
| 内存与控制流劫持攻击       | 内存缓冲区溢出   | 可攻破         | 不可攻破     | UART物理接口攻击      | 监听物理接口       | 可攻破         | 不可攻破     |
| 多系统的虚拟机逃逸攻击     | 虚拟机下指针越界 | 可攻破         | 不可攻破     | I2C物理接口攻击       | 监听物理接口       | 可攻破         | 不可攻破     |
| Spectre幽灵漏洞攻击        | 预测执行指令     | 可攻破         | 不可攻破     | Linux操作系统漏洞攻击 | 存在新老系统漏洞   | 可攻破         | 不可攻破     |
| Meltdown熔断漏洞攻击       | 处理器乱序执行   | 可攻破         | 不可攻破     | 中间人网络攻击        | ARP缓存和DNS缓存   | 可攻破         | 不可攻破     |
| 固件镜像代码获取和逆向攻击 | 读取存储和反汇编 | 可攻破         | 不可攻破     | Ptrace内存攻击        | 系统调用访问       | 可攻破         | 不可攻破     |
| 数据窃取及文件系统攻击     | 文件系统读取     | 可攻破         | 不可攻破     | 双重异常攻击          | 异常中再次进入异常 | 可攻破         | 不可攻破     |
| 缓存侧信道攻击             | 处理器缓存机制   | 可攻破         | 不可攻破     |                       |                    |                |              |

**结论：安全管控子系统安全 + 基于安全管控子系统的终端产品安全!**

---

## 二十三、计算终端产品特性对比

**(表格对比了四款信创计算终端与本方案（安全信创计算终端）的特性)**

| 特性                       | 信创计算终端1                                                      | 信创计算终端2                                                | 信创计算终端3                                                              | 信创计算终端4                                                      | **安全信创计算终端 (本方案)**                                                                                                         |
| -------------------------- | ------------------------------------------------------------------ | ------------------------------------------------------------ | -------------------------------------------------------------------------- | ------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- |
| **身份认证**         | 多因子强身份认证                                                   |                                                              | 多源登录与身份同步, LDAP、AD等协议支持, 多因子认证(MFA)支持                | USBKEY与PIN码双因素认证                                            | **多因素身份识别(人脸,指纹)。独立与第三方认证系统集成：支持独立用户管理与对接第三方认证系统, 实现多因子认证, 增强身份验证的安全性。** |
| **加密算法**         | 国密算法加密存储                                                   | 国密与国际算法融合                                           | 国密协议支持                                                               |                                                                    | **国密算法和国际算法双支持**                                                                                                          |
| **通信协议**         | 国密SSL安全通道                                                    | 安全通信保障                                                 | SSH/RDP/VNC协议支持                                                        |                                                                    | **基于国密IPSEC和国密私有协议，安全通信通道**                                                                                         |
| **访问控制**         | 实时运维监控与阻断                                                 |                                                              | 安全赋能与细粒度权限控制, 命令过滤与动作授权, 基于角色的权限访问控制(RBAC) |                                                                    | **细粒度访问控制和权限控制，实时监控用户操作，包括时间，地点，行为，权限的控制**                                                      |
| **行为审计**         |                                                                    | 审计与监控                                                   | 历史会话回放与实时监控, 安全审计与实时监控                                 | 安全审计与行为监控                                                 | **用户行为审计和异常操作告警**                                                                                                        |
| **安全标准**         | 国密标准全面支持                                                   |                                                              |                                                                            |                                                                    | **安全合规性标准遵循**                                                                                                                |
| **其他附加**         | 全Web访问技术, 灵活的多场景部署                                    | 全生命周期密钥管理, 密钥安全管理, 密钥分发机制, 密钥恢复能力 | Web Terminal访问体验                                                       | 实时策略下发机制, 多级数据备份与恢复, 多级管理架构, 安全策略自定义 | **定制化白名单安全策略, 实时安全策略, 数据脱敏保护技术, 全Web访问技术, 密钥管理技术**                                                 |
| **专有硬件安全方案** |                                                                    |                                                              |                                                                            |                                                                    | **可信区域多重存储机制, UKEY安全写入技术, 机箱智能防拆机制, USB硬件接口管控及硬盘自销毁机制, USB口管控(U盘, 鼠标键盘等外设)**         |
| **遵循标准**         | GB/T 39786标准符合性: 满足国标要求, 提供全面国密算法应用和改造方案 | 动态扩展架构                                                 |                                                                            |                                                                    | **GB/T 39786标准符合性: 满足国家标准GB/T 39786的要求, 提供全面的国密算法应用和改造解决方案。**                                        |

---

## 二十四、安全计算终端 - 安全防护 (真正遇到系统攻破后)

**(表格对比了有无安全处理器管控下的攻防效果)**

| 攻击内容                               | 无安全处理器管控                                                 | 有安全处理器管控                                                                                                       |
| -------------------------------------- | ---------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------- |
| **硬盘数据**                     | 数据被拷贝和被转移                                               | 攻破系统后, 系统会触发安全处理器的自动销毁硬盘功能, 把关键数据清除和销毁, 无法拷贝到关键数据。                         |
| **设备登录**                     | 设备可在任何时间, 地点登陆, 攻击者可以携带设备外出违规进行操作。 | 安全处理器会检查RTC时间和定位, 即使系统被攻破, 也会判断实时RTC和位置信息, 不让系统开机。                               |
| **数据通过网络转移**             | 设备被攻破后, 可以通过网络的方式, 将本机的数据传输给外界。       | 网络传输的数据都会经过安全处理器的处理, 这些数据是经过VPN隧道传输, 没有正确建立隧道, 是无法外发任何网络数据到外界。    |
| **通过USB口攻击, 例如插入U盘等** | 设备被攻破后, 可以利用U盘拷贝主机的敏感资料和内容。              | 安全处理器针对USB口进行管控, 包括可以对非法U盘的识别, 和对鼠标键盘的管控, 主机数据无法通过U盘拷贝走。                  |
| **敏感信息篡改**                 | 设备被攻破后, 可以修改一些安全的信息, 比如用户和权限。           | 设备被攻破后, 无法修改关键信息, 因为一些白名单信息和权限信息是储存在安全处理器的存储器中, 需要专用授权接口才可被修改。 |

**核心目标：有效保护计算终端数据安全，杜绝信息泄露！**

---

## 二十五、开案中产品：手持移动终端

***面临的问题：** 手持移动终端在软件系统和硬件结构方面都存在不同的安全漏洞，这些安全漏洞会导致手持移动终端处于可能遭受攻击的风险之中，除了数据泄露或者系统瘫痪等应用功能以外，甚至导致恐怖袭击等更加恶劣事件，例如黎巴嫩多地发生的传呼机和对讲机爆炸事件是一个典型案例。

***手持移动终端系统漏洞示例 (左侧图示及文字)：**
    1.**软件更新不及时：** 许多手持移动终端使用的操作系统和应用软件未能及时更新，导致已知的漏洞无法修补，给攻击者提供了可乘之机。
    2.**缺乏加密通信：** 某些手持移动终端在通信过程中缺乏有效的加密措施，通信数据以明文形式传输。
    3.**身份认证机制薄弱：** 部分移动终端在用户身份认证方面存在漏洞，未能有效验证用户身份。
    4.**缺乏远程管理和监控：** 许多手持移动终端缺乏远程管理和监控功能，无法实时监测和响应安全威胁。
    5.**硬件结构缺乏防护机制：** 许多手持移动终端无法防止攻击者恶意拆机并植入攻击设备。

***解决方案 (右侧图示及文字，以“解决方案”为中心，箭头指向各项措施)：**
    ***核心理念：** 极小增加原有方案成本的基础上实现。
    ***防拆机，防止手持移动终端被非法拆解、篡改或逆向工程：** 通过芯片内置的传感器及外机感应线圈，检测终端外部的物理拆卸行为，设置拆机后的执行命令如锁定、数据销毁甚至整机破坏等。
    ***防掉包，高安全性的软硬件融合安全管控系统：** 多级多因素的产品序列识别校验，以确保下单物品是原厂发货单次。
    ***国密传输私有安全通信协议：** 可配置私有传输环境，配置兼容TCP/IP或局域无线通信协议的安全私有协议，保障手持移动终端的通信安全，实现数据通信的隐私和防入侵。

---

## 二十六、谢谢

**(机构Logo: 西交网络空间安全研究院 - XIJIAO INSTITUTE FOR CYBER SCIENCE AND TECHNOLOGY)**

* 诸暨市浦阳路18号科创园3#楼
  *0575-87779388
* www.csa-xje.edu.cn
* (右下角有二维码)
