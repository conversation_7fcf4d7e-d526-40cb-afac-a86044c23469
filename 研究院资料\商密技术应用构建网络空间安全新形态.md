# 商密技术应用构建网络空间安全新形态 - 西交网络空间安全研究院

**芯片研发中心主任 梁梦雷**

**2025年5月22日**

---

## 目录 (CONTENTS)

* 研究院简介
* 技术积累 (商用密码)
* 技术应用方向

---

## 一、研究院简介

**(此页内容与前一份PPT的P11基本一致，包含一张城市风景/园区图片和文字介绍)**

* 西交网络空间安全研究院由诸暨市人民政府与西安交通大学共建。
* 中国国科学院院士、西安交通大学电子与信息学部主任管晓宏担任院长和首席科学家, 面向网络与数据安全领域科技发展前沿, 集聚高层次研发管理人才, 实施科技成果转移转化。
* 研发网络安全自主可控专用芯片、软硬结合内外网安全管控系统核心技术, 提供行业安全管控整体解决方案, 建设安全专用芯片研发中心、安全管控系统研究中心、行业解决方案三个研究中心及创新创业服务平台、合作交流服务平台两个服务平台, 共建智能物联网络与数据安全重点实验室。

---

## 二、研究院院长、首席科学家：管晓宏院士

**(此页内容与前一份PPT的P12基本一致，为管晓宏院士的个人照片及简介)**

***管晓宏院士**
***荣誉与资质：**
    *   教育部先进集体、黄大年式教师团队负责人
    *   清华大学本科、硕士, 美国康涅狄格大学博士, 哈佛大学访问科学家
    *   中国科学院院士、IEEE Fellow
    *   西安交通大学电子与信息学部主任、教授
    *   智能网络与网络安全教育部重点实验室首席科学家
    *   国家自然科学基金创新群体负责人
    *   主持国家自然科学二等奖2项
    *   何梁何利科学与技术进步奖
    *   美国李氏基金杰出成就奖等国际奖励

---

## 三、人才聚集

* 中国科学院院士1人
* 长江学者特聘教授5人, 青年长江5人
* 国家杰出青年基金获得者2人
* 国家优秀青年基金获得者5人
* 国家级人才计划和青年人才计划5人

---

## 四、研究院团队成果

| 类别                 | 主要成果                                                                                                                                                                                                                                                                                 |
| :------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **科技奖励**   | 实验室获得国家三大奖8项, 国家级教学奖6项, 以及省部级一等奖11项, 何梁何利科技进步奖、阿里巴巴青橙奖及多项国际奖; 系统安全优化等多个原创性理论与方法, 获得2018年国家自然科学二等奖; 多模态碎片知识融合、大数据融合处理与重大应用, 在国家税务风险识别系统应用, 获得2017年国家科技进步二等奖 |
| **科研项目**   | 国家重点研发、自然基金创新群体、自然基金重大、重点项目等, 总到款超过10亿元, 成果转化超过1.6亿元                                                                                                                                                                                          |
| **论文、专著** | 期刊论文1800余篇, ESI高被引论文200余篇。国际专著15部, 国内专著33部                                                                                                                                                                                                                       |
| **发明专利**   | 获得授权发明专利1700余项, 牵头制定国家和行业地方标准26项                                                                                                                                                                                                                                 |

**(下方附有多张获奖证书图片，包括：国家自然科学奖、国家科学技术进步奖（多项）、何梁何利基金科学与技术进步奖、达摩院青橙奖、国家级教学成果奖等。)**

---

## 五、与华为公司产品线和实验室深度合作

***高端芯片设计优化取得重大突破**
    *   限定制程下极致提高PPA (性能、功耗、面积)
***5G及未来无线通信系统节能降本取得重大突破**
    *   丝毫不影响性能前提下, 5G基站系统节能降本15-20%
***网络与智能系统安全**
    *   智能系统攻防对抗理论与技术, 弥补通用AI安全与隐私的加固能力, 解决了网络应用识别相关的网络安全问题

**(下方附有相关活动照片：管晓宏院士在华为公司首届系统工程大会演讲、西安交大团队访问华为、合作获奖（网络安全领域2020年度优秀合作伙伴MVP奖：沈超教授团队、罗兰贝格团队）等。)**

---

## 六、高端芯片设计优化成果

***获2022年华为杰出合作成果奖 (3000多个项目中选出10个)**
***“解决了芯片优化设计领域的卡脖子问题, 为打造芯片极致的PPA竞争力做出了卓越贡献”**

**(右侧为两份荣誉证书和一份项目成果应用证明的图片)**

***杰出合作成果奖 - 荣誉证书 (授予：西安交通大学 管晓宏院士)**
    *   您所负责的华为海思“序优化技术合作项目”成果优秀, 特此授予“杰出合作成果奖”。
    *   盖章：华为技术有限公司，时间：2022年5月
***杰出合作成果奖 - 荣誉证书 (授予：西安交通大学 翟桥柱教授)**
    *   您所负责的华为海思“序优化技术合作项目”成果优秀, 特此授予“杰出合作成果奖”。
    *   盖章：华为技术有限公司，时间：2022年5月
***项目成果应用证明**
    ***项目名称：** 序优化技术合作
    ***项目负责人：** 翟桥柱 (西安交通大学)
    ***内容摘要：** 西安交通大学翟桥柱教授及其领导的研究团队完成的序优化技术合作项目利用序优化技术，完成在万兆亿芯片设计的参数空间中，寻找到满足业务需求的极致PPA参数组合，协助提升DSE系统框架的可解释性。新的技术方案可以显著改善设计算法PPA收敛慢、场景感知差、可解释理论不完备、结果不可信等问题。
        翟桥柱教授及其领导的研究团队与华为海思专家紧密合作，深入分享复杂系统优化建模技术与算法设计思想方法，设计和建立精细模型寻找最佳配置，并完成了冷热启动和Pareto Front探索多套方案，为DSE成功应用奠定坚实的基础。相较当前业界最佳的AI高斯黑盒寻优，项目所提方法可解释性更高，结果可信，结合机理分析更容易被产品线和芯片架构师接受。在实际落地的场景中，通过提升了单次迭代可信度，可以减少30+%迭代计算量和20+%的迭代轮数。
        本项目着力于解决了芯片设计优化子领域的EDA卡脖子问题；同时助力海思DSE团队建设，培养了技术人才，为DSE技术落地和打造芯片的极致PPA竞争力做出了卓越贡献。
    ***注：** 本证明仅用于翟桥柱教授团队申报国家或者省部级科研项目和奖项、国家人才计划、学校学科评估的企业应用支撑材料目的使用，未经华为技术有限公司事先书面许可，不得为其他目的使用。
    ***盖章：** 华为技术有限公司，日期：2023年2月22日

---

## （重复的目录页）

**(此页P9与P2内容基本一致，为目录，指向“研究院简介”、“技术积累(商用密码)”、“技术应用方向”。)**

* 研究院简介 (已覆盖)
  ***技术积累 (商用密码)** (接下来的内容)
* 技术应用方向 (后续内容)

---

## 七、安全管控技术理念

**(图示说明：一个详细的系统架构图，与前一份PPT P6的“计算终端产品系统架构图”类似，但标题为“安全管控技术理念”，并增加了右侧的特性列表。)**

***核心组件：安全管控模组**
    *   审计存储
    *   电池
    *   网络接口 (旁路管控、北斗)
    *   USB/DVP 接口 (旁路管控)
    *4G/5G 模块
    *   连接到 **主CPU及系统**
***外部接口与设备：**
    *   带内/外监控
    *   认证 (指纹/动态密码等)
    *   用户鼠标/键盘/视频 (USB/DVP)
    *   USB PORT1, PORT2, 3, 4...
    *   USB HUB
    *   USB 外设
    *   电源控制
***专用接口授权**
***右侧列出的安全特性：**
    *   安全启动机制
    *   数据传输加密
    *   设备可控黑白名单
    *   国密硬件加速
    *5W安全策略
    *   专用私有协议
    *   设备智能防拆机
    *   系统“防篡改防拷贝”

---

## 八、设备级SoC结构

* 非安全核心和安全核心隔离执行架构, 通过硬件强制隔离和访问控制机制确保了对CPU、总线、中断和内存等关键模块的“**不可绕过、不可更改**”安全访问。这种全面的硬件安全方法为可信计算和安全数据处理奠定了坚实的基础。

***Fig. 1. Security management in ECCO (Edge, Cloud/Data center, Collaborative Defense)**
    ***(左侧图示：一个三层模型，底层是Device Hardening，中间是Edge，顶层是Cloud/Data center)**
    ***Device Hardening:**
        *   Basic Crypto function: Encryption accelerator, Unique Key gen., Secure Protocol
        *   Physical security protection: Tamper-resistant, Sensitive Data, Reset
        *   Formal security verification: Consistency, Model functionality, Reach ability
    ***Edge:**
        *   Attack Mechanism Analysis, Data, Model, system-> Collaborative Defense
    ***Cloud/Data center:**
        *   Defense strategy construction, Model Security Diagnosis, Attack Behavior Diagnosis

***Fig. 2. SAIAN security-aware SoC system structure**
    ***(右侧图示：一个SoC系统框图)**
    ***Security-awared OS:** Cryptographic Algorithm Program <->Cross-compile and link <-> Program Library on S-ISA. (Privileged mode (Trigger Emergency Response))
    ***Security Control Coprocessor:**
        *   SM2 Crypto Accelerator
        *   SM3 Crypto Accelerator
        *   SM4 Crypto Accelerator
        *   Hybrid RNG
    ***Tamper-proof security verification:**
        *   Quad DMA Controller, Peripherals, GIC, BP147
        *   Main Memory, Sec_Memory, WDT, Timer
    ***AMBA Bus**
    ***CPU Cores:** Quad-core CPU, Single-core CPU
    ***Memory interface, S-ISA**
    ***Overall block:** Security-awared SoC

---

## 九、基于硬件、软件协同设计的安全控制

***核心优势：提高运行效率的同时保证可迭代、可配置性。**

* SoC特权模式下的安全启动检测实现全面安全验证链, 硬件锚定的信任根(RoT)开始。初始化依次验证数字签名、验证存储在安全EFUSE中的加密密钥, 并对引导加载程序组件和系统固件进行完整性检查。验证步骤失败, 系统将立即停止引导过程并触发安全措施, 确保系统仅从受信任和未修改的软件组件启动, 从而在启动时就维持一个安全的操作环境。
* 此安全启动机制与SoC的中断处理集成, 并通过BP147保持执行效率, 从而在引导过程中同时确保安全性和性能。

***右表：安全启动检测过程在666MHz和1.2GHz下的处理步骤和执行时间 (Run Timein ARM(ms))**

| 安全启动检测过程                     | SMx | 666 MHz          | 1.2 GHz          |
| :----------------------------------- | :-- | :--------------- | :--------------- |
| SM2密钥对生成                        | SM2 | 52.45            | 29.07            |
| 哈希公钥                             | SM3 | 0.03             | 0.02             |
| 将密钥写入EFUSE                      |     | -                | -                |
| 准备引导加载程序二进制数据 (BIN生成) |     | -                | -                |
| 使用私钥签名BIN                      | SM3 | 511.60           | 283.20           |
| 将公钥和签名写入闪存                 |     | 55               | 30.46            |
| 芯片启动时从EFUSE读取密钥            |     | -                | -                |
| 从闪存读取并哈希公钥                 |     | -                | -                |
| 比较密钥与密钥1                      | SM3 | 0.03             | 0.02             |
| BOOT ROM从闪存读取签名               |     | -                | -                |
| 使用公钥验证签名                     |     | -                | -                |
| 加载并执行引导加载程序               | SM2 | 101.28           | 55.38            |
| **总时间 (安全启动检测过程)**  |     | **720.36** | **398.15** |

---

## 十、双域椭圆曲线密码 (ECC) 加速器技术

* 应用于SOC芯片设计, 实现了一个统一的算术单元, 能够高效地处理素数域 (P-256/P-544) 和二进制域 (B-576) 操作, 具有动态切换机制和优化的数据路径以实现并行域操作。
* 在SM2算法中, 标量乘法 (也称为点乘法, PM) 是核心组件, 并占据算法大部分时间, 效率直接决定了整体速度。随着侧信道攻击技术的发展, 传统的简单攻击方法如功耗分析 (SPA) 或差分功耗分析 (DPA) 已不再是唯一的威胁, 新的高阶侧信道攻击对加密算法实现提出了更严峻的挑战, 如基尼不纯度指数攻击 (GIA) 或模板攻击 (TA)。在这些新型攻击中, PM特别容易受到攻击。在抵御攻击方面, 该技术具备两个优势：
  1. 改进了k值的NAF编码算法, **采用了一种NAF编码随机化算法**, 增加了攻击者预测和分析计算模式的难度。NAF编码随机化算法与真随机数生成器 (TRNG) 单元集成有效地抵抗基于模板的功耗分析攻击。
     2.**随机化w-NAF将示例中的香农熵增加了50%**, 使得操作序列更难预测。当最小熵大于1位时, 攻击者难以猜测下一个操作的概率。

***(右侧图示：SM2 with Randomized w-NAF 流程图)**
    *   Input: SM2 Signature Input
    *   Step 1: Randomized w-NAF PM
    *   Step 2: HRNG (w, 1) (Hardware Random Number Generator)
    *   Step 3: NAF w-encoding (k_i) and Precompute[j]P
    *   Step 4: Main Loop (2p, ±jP) -> PM -> ECC (Output)

---

## 十一、PM中的动态存储调整技术

* 静态存储对侧信道分析提供的防御有限。为了保护免受功耗分析等类似攻击, 该技术采用了**预计算点的动态存储方案**, 包括存储位置的随机化、具有随机定时的安全点数据交换和优化的内存管理。
  ***(左侧图示：动态存储调整示意图)**
  * 包含 TNRG unit, Bias calculator, NAFw(k):ki decoder, W/R Enable /P, Round start, Register Shuffler, Precomputation unit (存储 0, IP, 2P, ..., (2^(w-1)-1)P, (2^(w-1))P )
    ***(右侧表格：定量分析比较表)**

| Category                                   | Fixed Storage Scheme            | Dynamic Storage Scheme             |
| :----------------------------------------- | :------------------------------ | :--------------------------------- |
| Randomness Quantification                  | Storage Location Entropy H(L)=0 | Storage Location Entropy H(L)=W    |
| Probability of accessing the i-th register | P(i) =1, fixed                  | P(i) =1/2^W, uniformly distributed |
| Key Recovery Complexity                    | C' = 2^H - ΔH*                 | C = 2^H = 1                        |
| Resistance to Side-Channel Attacks         | Vulnerable                      | Resistant                          |

* 预计算点与寄存器之间的映射是定期随机化, 算法确保相同的点不会在寄存器中存储太长时间。所有计算完成后, 洗牌器对寄存器数组执行单向循环滚动一步, 更新偏差值, 以确保后续PM计算的寄存器映射随机化, 从而在保持低功耗的同时增强安全性。

---

## 十二、混合随机数生成器单元 TRNG 技术

* 该技术应用于密钥系统中具有很强安全性, 特点是物理随机性实现设计在SoC中, 适用于对称加密算法 (如DES、AES、IDEA等) 和非对称加密算法 (如RSA、DSA、Diffie-Hellman等)。现有的TRNG实现方法主要包括基于热噪声、环形振荡器 (RO) 和亚稳态的设计。基于RO的TRNG由于其全电路实现特性和良好的集成性, 在物联网IoT设备中具有广阔的应用前景。
* 该TRNG方案采用改进的RO结构和创新的后处理算法, 在优化硬件资源利用的同时确保随机性质量, 注重在低功耗和小面积约束下的实施有效性, 以及在不同应用环境中的性能稳定性。不仅解决了PRNG的熵不足和可预测性问题, 还通过物理隔离显著提升了抗攻击能力。

  * **内置4路独立随机振荡源；**
  * **多振荡器组合冗余熵源和纠错机制；**
  * **内置数字线性反馈移位随机数处理器；**
  * **支持软件定义随机掩码值种子；**
  * **支持频数自检、全0或全1自检和环路自检；**
* **(右侧图示：一个柱状图，P-value vs. Test Item)**

  * 显示了不同测试项 (Block Freq, LongestRun, Rank, FFT, Non.Ov.Templ, Ov.Templ, Univ, Serial, Lin.Compl, Cumul.Sums, Rand.Ex., Rand.Exc.Var., Aper.Entro) 在三种不同条件下 (384Mlog1, 384Mlog2, 384Mlog3) 的P值。
  * 图注：右图为该技术测试的P值均超过10^(-4)阈值, 即使在极端温度 (80°C) 也具备显著性。随着温度变化的所有测试的通过比例均≥0.975且最小波动 (0.22-0.97) 稳定, 表明其始终符合随机性认证标准。

---

## 十三、SM3和SM4加密加速器技术

* **SM3算法**的硬件高效实现, 包括消息扩展模块、压缩函数单元和控制逻辑, 在该技术应用中优化为并行处理。压缩函数作为核心计算组件在硬件中实现, 其他部分则在软件中实现。SM3与SHA系列算法相似, 主要区别在于组长度、输出哈希值长度和替换压缩函数中的逻辑操作。
* **SM4算法**具有32轮非线性迭代结构, 用于加密和密钥扩展算法。主要特点包括支持电子密码本 (ECB) 和密文分组链接 (CBC) 模式。该技术中的硬件实现采用紧凑设计, 算法的简化密钥调度降低了硬件复杂性, 同时保持了安全性。轮函数使用S-boxes和线性变换来实现, 针对从高性能ASIC到资源受限FPGA的硬件平台进行优化。

---

## （再次重复的目录页）

**(此页P17与P2, P9内容基本一致，为目录，指向“研究院简介”、“技术积累(商用密码)”、“技术应用方向”。)**

* 研究院简介 (已覆盖)
* 技术积累 (商用密码) (已覆盖)
* **技术应用方向** (接下来的内容)

---

## 十四、现状：基于边界的网络安全防护

**(此页内容与前一份PPT P3 及本PPT P18 OCR结果基本一致，展示了“基于边界的网络安全防护”的典型场景及思考。)**

* **(图示说明：一个复杂的网络拓扑图，展示了“基于边界的网络安全防护”的典型场景)**
  * **核心要素：** 中央是连接到 **Internet** 的设备，部署了**防火墙、入侵检测、安全审计**。这构成了“**传统边界**”。
  * **外围连接与应用：** 远程监测互联、万物互联 (IoT)、车联远控、环境感知、车况监测、远程抄表、远程检测。
  * **突出问题：** **问题频发!**
* **思考：**
  * 现有攻击手段有哪些?
  * 防护手段有哪些?
  * 防护手段的共性问题和边界安全的缺陷在哪里?
  * 有没有新型的防护思路可以弥补缺陷?

---

## 十五、现状：常见攻击方式

**(此页内容与前一份PPT P4 及本PPT P19 OCR结果基本一致，展示了多种常见的网络攻击方式及其案例。)**

1. **中间人攻击 (MITM)** (案例: 乌克兰电网攻击, OpenSSL心脏出血)
2. **0day / 1day / nday 攻击** (案例: 宝洁, 多伦多市政府, 华为Eudemon防火墙)
3. **社会工程学攻击** (案例: Skymavis加密货币被盗, 推特大规模账户被黑)
4. **COTTONMOUTH-1 / 单机木马攻击** (案例: 西安某医院, "USBee" 软件)
5. **密码爆破攻击** (案例: LinkedIn数据泄露, Equifax)
6. **DDoS攻击** (案例: 巴西银行金融机构, GitHub)

---

## 十六、现状：国内网络安全产业全景图

**(此页内容与前一份PPT P5 及本PPT P20 OCR结果基本一致，包含一个柱状图和一个饼图，分析国内网络安全产业情况及问题。)**

* **左侧柱状图：各领域申报/收录情况对比** (显示众多细分安全领域及其相对热度)
* **右侧饼图：各领域营收占比及竞争热度** (网络与通信安全占比最高20.6%)
* **行业数据：** 510家安全厂商, 4941个产品, 1051亿元规模
* **行业问题总结：**
  1. 产品同质化严重，85%以上集中在“边界防护”，终端防护少但问题多。
  2. 实现方式多基于操作系统和软件。
  3. 产值不以技术为导向，厂商“战国纷争”。
  4. 缺乏能实现多种防护手段的技术形式。

---

## 十七、基于芯片的安全管控系统, 从根本上解决问题

**(此页内容与前一份PPT P8 及本PPT P21 OCR结果基本一致。)**

* **核心理念：** 研发物理层的网络安全管控芯片, 构建一体化网络安全管控系统, 为“云边端”内外网穿越, 物联网安全终端的互联, 建立安全通道。
* **两大关键点：**
  1. **01:** 从硬件层面保护数据资源免于非授权访问、篡改。
  2. **02:** 建立芯片级安全通信专用通道, 实现数据和通道两方面的安全管控。

---

## 十八、搭建基于芯片的安全管控体系

**(此页内容与前一份PPT P9 及本PPT P22 OCR结果基本一致，阐述芯片级安全体系的构成。)**

1. **芯片安全体系 (左侧向上箭头指示构建方向)：**
   * 芯片安全体系 -> CPU自主拓展安全管控指令集 -> 芯片硬件固有安全 -> 芯片固件安全策略 -> 嵌入式安全管控操作系统 -> 国密多场景网络传输协议 -> 安全多要素应用策略
2. **独有的固有安全存储机制 (中间)：**
   * 永久不可更改存贮空间 (高限制性写存贮区, 用户数据区)
   * 硬件触发和芯片特权模式
   * 硬件OS自检策略 (CPU缓存安全区清除, 芯片数据安全通道格式)
   * **密文运算速率2Gbps**
3. **创新性的功能区域划分 (右侧)：**
   * 安全管控功能区域 (业务区域, 安全固件及策略区域)
   * 硬件实现真随机发生及算法加速 (芯片动态真随机发生器, 国密算法硬件加速, 现场可编程状态机和数据直接搬运)

---

## 十九、安全管控芯片介绍

**(此页内容与前一份PPT P13 及本PPT P23 OCR结果基本一致，为安全管控芯片“赛安二号”的详细框图和特性说明。)**

* **芯片框图 (详细，包含众多模块，如ARM Cortex-A53, Cortex-M4F,各种接口和安全模块)**
* **主要特性：**
  * **高处理性能：** 4核Cortex-A53,1.2GHz; 单核Cortex-M4F.
  * **高密文数据吞吐：** 算法硬件加速,1Gbps; 高速接口.
  * **芯片安全体系：** RTC/白名单不可更改; CPU/总线等不可绕过; 锁定JTAG/efuse.

---

## 二十、赛安一号芯片商用密码产品认证

**(左侧为芯片实物图，右侧为“商用密码产品认证证书”图片)**

* **GM 商用密码产品认证证书**
  * **证书编号：** GM003212020230182
  * **委托人名称及所在地：** 苏州赛博网铠信息科技发展有限公司 (中国(江苏)自由贸易试验区苏州片区苏州工业园区星湖街328号C-7(欧瑞大厦)506单元)
  * **生产者(制造商)名称及所在地：** 苏州赛博网铠信息科技发展有限公司 (同上)
  * **生产企业名称及所在地：** 厦门码灵半导体技术有限公司 (福建省厦门市海沧区海沧大道567号厦门中心E栋12层)
  * **产品名称和型号、版本：** 赛安一号安全芯片 CPNT2101NSMC V1.0
  * **产品标准和技术要求：** GM/T 0008《安全芯片密码检测准则》第一级要求
  * **结论：** 上述产品符合商用密码产品认证规则的要求, 特此发证。
  * **颁发日期：** 2023年3月17日
  * **有效期至：** 2028年3月16日
  * **签发人：** 牛路宏
  * **发证机构：** 国家密码管理局商用密码检测中心

---

## 二十一、安全可信嵌入式操作系统

**(此页内容与前一份PPT P14 及本PPT P25 OCR结果基本一致。)**

* **核心思想：** 在CPU上同时隔离运行非安全核 (任务核) 和安全核 (管控核)。
  * **普通世界：** 非安全核运行，访问普通硬件，运行通用OS，执行普通应用。
  * **安全世界：** 安全核运行，访问所有硬件，运行安全OS，执行管控任务。
* **(图示：监控模式下，普通世界与安全世界隔离运行)**
* **关键特性：**
  * 可信操作系统与通用操作系统的切换
  * 私有审计存储开发
  * 专用接口授权
  * 5W安全管控开发多因素策略授权
  * 电源管控方案

---

## 二十二、安全管控私有传输协议

**(此页内容与前一份PPT P15 及本PPT P26 OCR结果基本一致。)**

* **协议定义：** 基于Diffie-Hellman密钥交换的轻量级传输层安全加密协议。支持多种加密算法，独特支持国密算法，提供完整加密通信、身份验证与完整性保护。
* **(图示：Diffie-Hellman (DH) 密钥交换过程)**
* **特点：**
  * 集成SM2, SM3, SM4国密算法
  * 模块化的协议框架
  * 可构建定制化的安全通信方案
  * 双向DH密钥交换机制
  * 支持多种不同类型DH函数
  * 多种不同类型的加密算法哈希函数

---

## 二十三、协议优势

* **数据加密：** 确保数据在传输过程中不被窃取或篡改。
* **身份验证：** 确保发送者的身份是可信的, 防止伪造。
* **完整性保护：** 通过使用哈希函数, 确保数据在传输过程中未被修改。
* **安全隧道：** 在公共网络上创建一个安全的“隧道”, 允许用户在不安全的网络中进行安全通信。
* **支持不同的认证方法：** 可以使用多种认证机制, 如数字证书等, 以增强安全性。

---

## 二十四、五维安全体系

**(此页内容与前一份PPT P16 及本PPT P28 OCR结果基本一致，展示了5W1H安全体系。)**

* **策略 (贯穿始终)**
* **Who (访问者)：** 身份/业务权限/生理特征等认证、授权。
* **When (访问时间)：** 独立时钟及电源控制。防止非授权。
* **Where (访问地点)：** 独立电池及北斗定位。防止非授权或者意外 (盗抢丢等)。
* **Which (访问方式/对象)：** 有线/WiFi/4G/5G/北斗等; 地址、端口; 明文/加密/签名/防伪等。
* **What (访问行为/内容)：** 本地记录与审计; 网关应用层业务行为审计。

---

## 二十五、基于AI的网络防御应用

**(此页内容与前一份PPT P17 及本PPT P29 OCR结果基本一致。)**

* **行为分析：** 静态文件AI、动态行为AI行为关联分析，检测和防护勒索病毒、漏洞利用，发现异常行为，识别恶意活动或未知威胁。
* **安全运营：** 实时监控网络流量、日志和安全事件。自动化渗透测试，提前防御潜在的攻击。
* **特征库：** 自动提取病毒的特征，降低特征库更新频率，提高安全防护的灵活性。
* **大语言模型的应用：** 应对网络钓鱼检测、恶意软件检测、人工智能深度伪造、自动化社会工程攻击等。

---

## 二十六、安全管控平台

**(此页内容与前一份PPT P18 及本PPT P30 OCR结果基本一致。)**

* **基于AI的多因素策略授权/审计 (2/3/4人等)**
* **根据业务岗位需求，确定安全策略**
* **(图示：多因素制约、授权、审计流程；存储区域划分示意图)**
* **核心优势：** 内部人员/OS无法更改策略固件; 软硬绑定防镜像逆向; 避免漏洞及人为风险。

---

## 二十七、综合应用场景

**(此页内容与前一份PPT P7 及本PPT P31 OCR结果基本一致，展示了“赛安”系列产品在不同应用场景中的部署方式。)**

* **终端设备：** 赛安安全电脑 (含赛安一、二号芯片) 应用于医疗、政务、重要行业远程指挥办公。
* **网络与平台：** 通过安全通信 -> 赛安安全网关 (含赛安三号芯片) -> 安全管控 -> 赛安安全管控平台 -> 安全服务器。
* **安全能力：** 安全加密、多因素授权、行为审计、电子围栏等。
* **应用场景：** 政务、司法、公安、医疗、金融等。

---

## 二十八、安全信创计算机

* **内生TCM可信密码及启动检测**
  * **安全启动检测：** 计算机启动时, 检查固件及操作系统等软件是否被非法修改, 确保系统从安全状态开始运行。
  * **认证与授权：** 在启动系统和应用程序时, 确认用户的身份, 确保只有被授权的人才能使用。
  * **数据加密和解密：** 硬件加密和解密, 保护信息不被未经授权的人读取。
  * **硬件安全监控：** 该功能可以进行硬件级别的非法访问和篡改尝试并采取相应的安全措施。比如检测到硬盘被非法替换, 可以对硬盘数据进行销毁等操作。
  * **建立安全信任根基：** 通过提供密钥保护等功能, 确保计算平台的安全性, 建立可信赖的基础。
* **可选功能：**
  1. **系统保护：**
     * 特点: 主系统业务需先通过安全协处理器判断, 包括外设接口合规性。对操作系统漏洞提供一层保护。
  2. **电子围栏：**
     * 特点: 使用时间与地点限定, 且由传统软件实现转为硬件配置。

---

## 二十九、开案中产品：手持移动终端

**(此页内容与前一份PPT P30 及本PPT P33 OCR结果基本一致。)**

* **面临的问题：** 手持移动终端软硬件安全漏洞导致数据泄露、系统瘫痪甚至恐怖袭击风险 (如黎巴嫩传呼机爆炸案例)。
* **漏洞示例：** 软件更新不及时、缺乏加密通信、身份认证薄弱、缺乏远程管控、硬件缺乏防护。
* **解决方案 (极小增加原有成本)：**
  * **防拆机：** 芯片内置传感器及外机感应线圈，检测物理拆卸，执行锁定/销毁等。
  * **防掉包：** 多级多因素产品序列识别校验，确保原厂发货。
  * **国密传输私有安全通信协议：** 配置私有传输环境，保障通信安全、隐私和防入侵。

---

## 三十、请领导专家指正

* **梁梦雷 13581555659**
* **联系地址：** 诸暨市浦阳路18号科创园3#楼
* **联系电话：** 0575-87779388
* **网址：** www.csa-xje.edu.cn
* **(右下角有微信公众号二维码)**
